@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.settings

import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppLineOptionText
import com.flutterup.app.design.component.AppTitleText
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.PurplePrimary
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.settings.state.AccountCenterOption

@Composable
fun AccountCenterScreen() {
    val navController = LocalNavController.current

    AccountCenterContent(
        onBackClick = navController::popBackStack,
        onDeleteAccountClick = { navController.navigate(DeleteAccountRoute) },
        onOptionClick = {
            when(it) {
                AccountCenterOption.CHANGE_PWD -> {
                    navController.navigate(ChangePwdRoute)
                }
            }
        },
    )
}

@Composable
private fun AccountCenterContent(
    onBackClick: () -> Unit = {},
    onDeleteAccountClick: () -> Unit = {},
    onOptionClick: (AccountCenterOption) -> Unit = {},
) {
    val options = remember { AccountCenterOption.entries }

    AppScaffold(
        title = { AppTitleText(stringResource(R.string.account)) },
        onBackClick = onBackClick,
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent),
        modifier = Modifier.fillMaxSize()
    ) { paddingValues ->
        Image(
            painter = painterResource(R.mipmap.ic_common_top_bg),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier.fillMaxWidth()
        )

        Box(
            Modifier.fillMaxSize().padding(paddingValues)
        ) {

            LazyColumn(
                modifier = Modifier
                    .padding(top = 15.dp)
                    .fillMaxSize(),
                contentPadding = PaddingValues(horizontal = 15.dp),
                verticalArrangement = Arrangement.spacedBy(10.dp)
            ) {
                items(items = options, key = { it.ordinal }) {
                    AppLineOptionText(
                        title = stringResource(it.titleRes),
                        iconDrawableRes = it.iconRes,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(50.dp)
                            .clickable(
                                onClick = { onOptionClick(it) },
                                role = Role.Button
                            )
                    )
                }
            }

            Box(
                modifier = Modifier
                    .padding(bottom = 32.dp)
                    .padding(horizontal = 24.dp)
                    .fillMaxWidth()
                    .height(50.dp)
                    .border(
                        width = 1.dp,
                        color = PurplePrimary,
                        shape = RoundedCornerShape(25.dp)
                    )
                    .align(Alignment.BottomCenter)
                    .noRippleClickable(
                        onClick = onDeleteAccountClick,
                        role = Role.Button
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = stringResource(R.string.delete_account),
                    style = TextStyle(
                        fontSize = 16.sp,
                        lineHeight = 22.sp,
                        fontWeight = FontWeight(500),
                        color = PurplePrimary,
                    )
                )
            }
        }
    }
}

@Preview
@Composable
private fun AccountCenterScreenPreview() {
    AppTheme {
        AccountCenterContent()
    }
}
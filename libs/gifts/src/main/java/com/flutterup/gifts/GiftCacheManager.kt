package com.flutterup.gifts

import com.flutterup.base.AppDirs
import com.flutterup.base.Dirs
import com.flutterup.gifts.database.GiftDatabase
import com.flutterup.gifts.entity.GiftCacheEntity
import com.flutterup.gifts.entity.GiftResourceInfo
import com.flutterup.network.AppClient
import com.flutterup.network.AppClients
import com.flutterup.network.AppDispatchers
import com.flutterup.network.Dispatcher
import com.flutterup.network.DownloadApiService
import com.flutterup.network.impl.NetworkServiceProvider
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject


class GiftCacheManager @Inject constructor(
    @Dispatcher(AppDispatchers.IO) private val dispatchers: CoroutineDispatcher,
    @Dirs(AppDirs.CACHE) private val fileDir: File,
    @AppClient(AppClients.DOWNLOAD) private val networkService: NetworkServiceProvider,
    private val database: GiftDatabase,
) {
    companion object {
        private const val IMAGE_SUFFIX = ".png"
        private const val GIF_SUFFIX = ".gif"
        private const val MP4_SUFFIX = ".mp4"
        private const val IMAGE_DIR = "gift/image"
        private const val GIF_DIR = "gift/gif"
        private const val MP4_DIR = "gift/mp4"
    }


    suspend fun cacheGiftIfNeeded(info: GiftResourceInfo) {
        val cachedGift = database.giftCacheDao().getGift(info.giftId)

        val imageFile = getGiftImageFile(info.giftId)
        val gifFile = getGiftGifFile(info.giftId)
        val mp4File = getGiftMp4File(info.giftId)

        var needDownloadImage = true
        var needDownloadGif = true
        var needDownloadMp4 = true

        if (cachedGift != null) {
            if (cachedGift.version >= info.version) { // 版本相同，检查文件是否存在
                if (imageFile.exists()) {
                    needDownloadImage = false
                }
                if (gifFile.exists()) {
                    needDownloadGif = false
                }
                if (mp4File.exists()) {
                    needDownloadMp4 = false
                }
            }
        }

        if (needDownloadImage && info.image != null) {
            downloadToFile(info.image, imageFile)
        }
        if (needDownloadGif && info.gif != null) {
            downloadToFile(info.gif, gifFile)
        }
        if (needDownloadMp4 && info.video != null) {
            downloadToFile(info.video, mp4File)
        }

        if (needDownloadImage || needDownloadGif || needDownloadMp4) {
            insertGifResource(info, imageFile, gifFile, mp4File)
        }
    }

    private suspend fun downloadToFile(url: String, destFile: File): File? {
        return withContext(dispatchers) {
            try {
                destFile.parentFile?.mkdirs()

                networkService[DownloadApiService::class.java].download(url).body()?.byteStream()?.use { input ->
                    FileOutputStream(destFile).use { output ->
                        input.copyTo(output)
                    }
                }
                destFile
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }

    fun getGiftImageFile(giftId: String, suffix: String = IMAGE_SUFFIX): File {
        return File(fileDir, "$IMAGE_DIR/$giftId$suffix")
    }

    fun getGiftGifFile(giftId: String, suffix: String = GIF_SUFFIX): File {
        return File(fileDir, "$GIF_DIR/$giftId$suffix")
    }

    fun getGiftMp4File(giftId: String, suffix: String = MP4_SUFFIX): File {
        return File(fileDir, "$MP4_DIR/$giftId$suffix")
    }

    private suspend fun insertGifResource(info: GiftResourceInfo, imageFile: File, gifFile: File, mp4File: File) {
        val giftCacheEntity = GiftCacheEntity(
            giftId = info.giftId,
            name = info.name,
            imagePath = imageFile.absolutePath,
            gifPath = gifFile.absolutePath,
            videoPath = mp4File.absolutePath,
            desc = info.desc,
            version = info.version
        )

        database.giftCacheDao().insertGift(giftCacheEntity)
    }
}

package com.flutterup.app.model

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass


@Keep
@JsonClass(generateAdapter = true)
data class StoreSubscriptionEntity(
    @Json(name = "rights") val rights: List<SubscriptionBenefitEntity>? = null,
    @Json(name = "shops") val shops: List<SubscriptionItem>? = null,
    @<PERSON><PERSON>(name = "page_desc1") val pageDesc1: String? = null,
    @<PERSON><PERSON>(name = "page_desc2") val pageDesc2: String? = null,
)

@Keep
@JsonClass(generateAdapter = true)
data class SubscriptionBenefitEntity(
    @J<PERSON>(name = "icon") val icon: String? = null,
    @<PERSON><PERSON>(name = "icon_unselect") val iconUnselect: String? = null,
    @Json(name = "title") val title: String? = null,
    @<PERSON><PERSON>(name = "desc") val desc: String? = null,
    @<PERSON><PERSON>(name = "background") val background: String? = null,
)

@Keep
@JsonClass(generateAdapter = true)
data class SubscriptionItem(
    @Json(name = "time") val time: String? = null,
    @Json(name = "unit") val unit: String? = null,
    @J<PERSON>(name = "save") val save: String? = null,
    @Json(name = "price") val price: String? = null,
    @Json(name = "average") val average: String? = null,
    @Json(name = "hot") val hot: Int? = null,
    @Json(name = "prod_id") val prodId: String? = null,
    @Json(name = "product_type") val prodType: String? = null,
    @Json(name = "subscribing") val subscribing: Int? = null,
    @Json(name = "name") val name: String? = null,
    @Json(name = "price_value") val priceValue: Int? = null,
    @Json(name = "price_fvalue") val priceFValue: Float? = null,
    @Json(name = "currency") val currency: String? = null,
    @Json(name = "priceId") val priceId: String? = null
)

@Keep
@JsonClass(generateAdapter = true)
data class PaymentPacksEntity(
    @Json(name = "list") val list: List<PaymentPacksItem>? = null,
    @Json(name = "description") val description: String? = null,
)

@Keep
@JsonClass(generateAdapter = true)
data class PaymentPacksItem(
    @Json(name = "id") val id: String? = null,
    @Json(name = "diamond") val diamond: Float = 0f,
    @Json(name = "name") val name: String? = null,
    @Json(name = "number") val num: Int? = null,
    @Json(name = "type") val type: Int? = null,
    @Json(name = "hot") val hot: Int? = null,
)

@Keep
@JsonClass(generateAdapter = true)
data class PaymentDiamondsEntity(
    @Json(name = "list") val list: List<PaymentDiamondsItem>? = null,
    @Json(name = "description1") val description1: String? = null,
    @Json(name = "description2") val description2: String? = null,
)

@Keep
@JsonClass(generateAdapter = true)
data class PaymentDiamondsItem(
    @Json(name = "id") val id: String? = null,
    @Json(name = "diamond") val diamond: Float = 0f,
    @Json(name = "name") val name: String? = null,
    @Json(name = "subname") val subName: String? = null,
    @Json(name = "price") val price: String? = null,
    @Json(name = "unit") val unit: String? = null,
    @Json(name = "prod_id") val prodId: String? = null,
    @Json(name = "icon") val icon: String? = null,
    @Json(name = "hot") val hot: Int? = null,
    @Json(name = "popular") val popular: Int? = null,
)
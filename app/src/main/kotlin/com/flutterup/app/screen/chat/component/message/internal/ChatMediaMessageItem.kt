package com.flutterup.app.screen.chat.component.message.internal

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import coil3.compose.AsyncImage
import com.flutterup.app.R
import com.flutterup.app.design.haze.DefaultHazeStyle
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.model.MediaMessageEntity
import com.flutterup.app.model.MultiMediaMessageEntity
import com.flutterup.app.model.UserInfo
import com.flutterup.app.utils.chat.message.PrivateMessageLockStatus.*
import com.flutterup.app.utils.chat.message.deleteTimeMillis
import com.flutterup.app.utils.chat.message.lockStatus
import com.flutterup.base.utils.DateUtils
import com.flutterup.chat.message.content.MultiPrivateMessageContent
import com.flutterup.chat.message.content.PrivateMessageContent
import com.flutterup.chat.message.content.PublicMessageContent
import dev.chrisbanes.haze.hazeEffect
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.imlib.model.MessageContent
import io.rong.message.ImageMessage
import io.rong.message.SightMessage
import kotlinx.coroutines.delay


@Composable
fun ChatMediaMessageItem(
    mine: UserInfo?,
    other: UserInfo?,
    message: Message,
    content: MessageContent,
    onResendClick: (Message) -> Unit,
    onProfileClick: () -> Unit,
    onMediaClick: (Message) -> Unit,
    onExpired: (Message) -> Unit,
) {
    if (message.messageDirection == Message.MessageDirection.SEND) {
        ChatMediaMessageItemSender(
            mine = mine,
            message = message,
            content = content,
            onResendClick = onResendClick,
            onMediaClick = onMediaClick
        )
    } else {
        ChatMediaMessageItemReceiver(
            other = other,
            message = message,
            content = content,
            onProfileClick = onProfileClick,
            onMediaClick = onMediaClick,
            onExpired = onExpired,
        )
    }
}

@Composable
private fun ChatMediaMessageItemSender(
    mine: UserInfo?,
    message: Message,
    content: MessageContent,
    onResendClick: (Message) -> Unit,
    onMediaClick: (Message) -> Unit,
) {
    ChatMessageRowSender(
        mine = mine,
        message = message,
        onResendClick = onResendClick
    ) {
        if (message.sentStatus == Message.SentStatus.SENDING) {
            Box(
                modifier = Modifier
                    .background(Color(0xFFD7D1E2), MEDIA_SHAPE)
                    .size(MEDIA_SIZE)
            )
        } else {
            ChatMessageMediaContent(
                message = message,
                content = content,
                onMediaClick = onMediaClick ,
                onExpired = {}, //发送方不需要超时
            )
        }
    }
}

@Composable
private fun ChatMediaMessageItemReceiver(
    other: UserInfo?,
    message: Message,
    content: MessageContent,
    onProfileClick: () -> Unit,
    onMediaClick: (Message) -> Unit,
    onExpired: (Message) -> Unit,
) {
    ChatMessageRowReceiver(
        other = other,
        message = message,
        onProfileClick = onProfileClick
    ) {
        ChatMessageMediaContent(
            message = message,
            content = content,
            onMediaClick = onMediaClick,
            onExpired = onExpired,
        )
    }
}

@Composable
private fun ChatMessageMediaContent(
    message: Message,
    content: MessageContent,
    onMediaClick: (Message) -> Unit,
    onExpired: (Message) -> Unit,
) {
    val singleModifier =  Modifier
        .size(MEDIA_SIZE)
        .clip(MEDIA_SHAPE)
        .noRippleClickable(onClick = { onMediaClick(message) })

    when(content) {
        is ImageMessage -> SingleMedia(
            type = MediaItemEntity.TYPE_IMAGE,
            modifier = singleModifier,
            model = content.mediaUrl
        )

        is SightMessage -> SingleMedia(
            type = MediaItemEntity.TYPE_VIDEO,
            modifier = singleModifier,
            model = content.thumbUri ?: content.mediaUrl
        )

        is PublicMessageContent -> {
            val data = content.get(MediaMessageEntity::class.java) ?: return
            SingleMedia(
                type = data.type,
                modifier = singleModifier,
                model = data.imageUrl
            )
        }

        is PrivateMessageContent -> {
            val data = content.get(MediaMessageEntity::class.java) ?: return
            SinglePrivateMedia(
                modifier = singleModifier,
                data = data,
                message = message,
                onExpired = onExpired
            )
        }

        is MultiPrivateMessageContent -> {
            val data = content.get(MultiMediaMessageEntity::class.java) ?: return
            MultiPrivateMedia(
                data = data,
                message = message,
                onMediaClick = onMediaClick,
                onExpired = onExpired,
            )
        }
    }
}

@Composable
private fun SingleMedia(
    type: Int,
    modifier: Modifier,
    model: Any?,
) {
    Box(modifier = modifier) {
        AsyncImage(
            model = model,
            contentDescription = null,
            contentScale = ContentScale.Crop,
            modifier = Modifier.matchParentSize()
        )

        if (type == MediaItemEntity.TYPE_VIDEO) {
            Icon(
                painter = painterResource(R.drawable.ic_media_video),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier
                    .padding(start = 6.dp, top = 4.dp)
                    .align(Alignment.TopStart)
            )
        }
    }
}

@Composable
private fun SinglePrivateMedia(
    modifier: Modifier,
    data: MediaMessageEntity,
    message: Message,
    onExpired: (Message) -> Unit,
) {
    val status = message.lockStatus()

    val mediaTypeText = if (data.type == MediaItemEntity.TYPE_IMAGE) {
        stringResource(R.string.private_photo)
    } else {
        stringResource(R.string.private_video)
    }

    when(status) {
        LOCKED -> {
            Box(modifier) {
                AsyncImage(
                    model = data.imageUrl,
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier.matchParentSize().hazeEffect(DefaultHazeStyle),
                )

                Column(
                    modifier = Modifier.align(Alignment.Center),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Image(
                        painter =  painterResource(R.mipmap.ic_chat_private),
                        contentDescription = null,
                        contentScale = ContentScale.FillBounds,
                        modifier = Modifier.size(25.dp)
                    )

                    Spacer(Modifier.height(4.dp))

                    Text(
                        text = mediaTypeText,
                        style = TextStyle(
                            fontSize = 10.sp,
                            lineHeight = 18.sp,
                            fontWeight = FontWeight.W400,
                            color = Color.White,
                        )
                    )
                }

                if (data.type == MediaItemEntity.TYPE_VIDEO) {
                    Icon(
                        painter = painterResource(R.drawable.ic_media_video),
                        contentDescription = null,
                        tint = Color.Unspecified,
                        modifier = Modifier.padding(start = 6.dp, top = 4.dp).align(Alignment.TopStart)
                    )
                }
            }
        }
        OPENED -> {
            Box(modifier) {
                AsyncImage(
                    model = data.imageUrl,
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier.matchParentSize()
                )


                if (data.type == MediaItemEntity.TYPE_VIDEO) {
                    Icon(
                        painter = painterResource(R.drawable.ic_media_video),
                        contentDescription = null,
                        tint = Color.Unspecified,
                        modifier = Modifier.padding(start = 6.dp, top = 4.dp).align(Alignment.TopStart)
                    )
                }

                CountdownText(
                    message = message,
                    deleteTime = message.deleteTimeMillis(),
                    onExpired = onExpired,
                    modifier = Modifier
                        .padding(end = 6.dp, top = 4.dp)
                        .align(Alignment.TopEnd)
                )
            }
        }
        EXPIRED -> ExpireContent(modifier)
    }
}

@Composable
private fun MultiPrivateMedia(
    data: MultiMediaMessageEntity,
    message: Message,
    onMediaClick: (Message) -> Unit,
    onExpired: (Message) -> Unit,
) {
    val size = data.entities.size
    val singleModifier =  Modifier
        .size(MEDIA_SIZE)
        .clip(MEDIA_SHAPE)
        .noRippleClickable(onClick = { onMediaClick(message) })

    if (size == 1) { //防止奇怪的数据出现
        val singleData = data.entities.first()
        SinglePrivateMedia(
            modifier = singleModifier,
            data = singleData,
            message = message,
            onExpired = onExpired
        )
        return
    }

    val status = message.lockStatus()

    val mediaTypeText = if (data.type == MediaItemEntity.TYPE_IMAGE) {
        stringResource(R.string.private_photos)
    } else {
        stringResource(R.string.private_videos)
    }

    when(status) {
        LOCKED -> {
            Box(
                modifier = Modifier.noRippleClickable(onClick = { onMediaClick(message) })
            ) {
                //只取前2张卡
                val first = data.entities.first()
                val second = data.entities.getOrNull(1) ?: first


                //第二张图在背后，先绘制
                AsyncImage(
                    model = second.imageUrl,
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier.size(MEDIA_SIZE)
                        .clip(MEDIA_SHAPE)
                        .hazeEffect(DefaultHazeStyle) {
                            this.blurEnabled = true
                        }
                        .rotate(10f)
                )

                //第一张图
                Box(
                    modifier = Modifier
                        .border(0.5.dp, Color.White, MEDIA_SHAPE)
                        .size(MEDIA_SIZE)
                        .clip(MEDIA_SHAPE)
                ) {
                    AsyncImage(
                        model = first.imageUrl,
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier.matchParentSize()
                            .clip(MEDIA_SHAPE)
                            .hazeEffect(DefaultHazeStyle) {
                                this.blurEnabled = true
                            }
                    )

                    Column(
                        modifier = Modifier.align(Alignment.Center),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Image(
                            painter =  painterResource(R.mipmap.ic_chat_private),
                            contentDescription = null,
                            contentScale = ContentScale.FillBounds,
                            modifier = Modifier.size(25.dp)
                        )

                        Spacer(Modifier.height(4.dp))

                        Text(
                            text = mediaTypeText,
                            style = TextStyle(
                                fontSize = 10.sp,
                                lineHeight = 18.sp,
                                fontWeight = FontWeight.W400,
                                color = Color.White,
                            )
                        )
                    }

                    if (data.type == MediaItemEntity.TYPE_VIDEO) {
                        Icon(
                            painter = painterResource(R.drawable.ic_media_video),
                            contentDescription = null,
                            tint = Color.Unspecified,
                            modifier = Modifier.padding(start = 6.dp, top = 4.dp).align(Alignment.TopStart)
                        )
                    }

                    MultiPrivateLabel(
                        count = size,
                        modifier = Modifier
                            .offset(x = 3.dp, y = (-3).dp)
                            .align(Alignment.TopEnd)
                    )
                }
            }
        }
        OPENED -> {
            Box(
                modifier = Modifier.noRippleClickable(onClick = { onMediaClick(message) })
            ) {
                //只取前2张卡
                val first = data.entities.first()
                val second = data.entities.getOrNull(1) ?: first


                //第二张图在背后，先绘制
                AsyncImage(
                    model = second.imageUrl,
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier.size(MEDIA_SIZE)
                        .clip(MEDIA_SHAPE)
                        .rotate(10f)
                )

                //第一张图
                Box(
                    modifier = Modifier
                        .border(0.5.dp, Color.White, MEDIA_SHAPE)
                        .size(MEDIA_SIZE)
                        .clip(MEDIA_SHAPE)
                ) {
                    AsyncImage(
                        model = first.imageUrl,
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier.matchParentSize().clip(MEDIA_SHAPE)
                    )

                    if (data.type == MediaItemEntity.TYPE_VIDEO) {
                        Icon(
                            painter = painterResource(R.drawable.ic_media_video),
                            contentDescription = null,
                            tint = Color.Unspecified,
                            modifier = Modifier.padding(start = 6.dp, top = 4.dp).align(Alignment.TopStart)
                        )
                    }
                }

                MultiPrivateLabel(
                    count = size,
                    modifier = Modifier
                        .offset(x = 3.dp, y = (-3).dp)
                        .align(Alignment.TopEnd)
                )
            }
        }
        EXPIRED -> ExpireContent(singleModifier)
    }
}

@Composable
private fun CountdownText(
    message: Message,
    deleteTime: Long,
    modifier: Modifier = Modifier,
    onExpired: (Message) -> Unit,
) {
    var remainingTime by remember { mutableLongStateOf(deleteTime - System.currentTimeMillis()) }

    LaunchedEffect(deleteTime) {
        while (remainingTime > 0L) {
            delay(1000)
            remainingTime -= 1000
        }

        onExpired(message)
    }

    if (deleteTime <= 0L) return

    Row(
        modifier = modifier
            .background(Color.Black.copy(0.4f), RoundedCornerShape(40.dp))
            .padding(horizontal = 8.dp, vertical = 3.dp),
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(R.mipmap.ic_chat_private_expire),
            contentDescription = null,
            contentScale = ContentScale.FillBounds,
            modifier = Modifier.size(6.dp)
        )

        Text(
            text = DateUtils.formatTimestamp(remainingTime, DateUtils.defaultCountdownFormat),
            style = TextStyle(
                fontSize = 10.sp,
                lineHeight = 18.sp,
                fontWeight = FontWeight.W400,
                color = Color.White,
            )
        )
    }
}

@Composable
private fun ExpireContent(modifier: Modifier) {
    Column(
        modifier = Modifier
            .background(Color(0xFFD7D1E2), MEDIA_SHAPE)
            .then(modifier),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Image(
            painter = painterResource(R.mipmap.ic_chat_private_expire),
            contentDescription = null,
            contentScale = ContentScale.FillBounds,
            modifier = Modifier.size(25.dp)
        )

        Spacer(Modifier.height(8.dp))

        Text(
            text = stringResource(R.string.chat_private_deleted),
            style = TextStyle(
                fontSize = 9.sp,
                lineHeight = 10.sp,
                fontWeight = FontWeight.W400,
                color = Color.White,
            )
        )
    }
}

@Composable
private fun MultiPrivateLabel(
    count: Int,
    modifier: Modifier = Modifier,
) {
    if (count <= 0) return

    Box(modifier) {
        Icon(
            painter = painterResource(R.drawable.ic_multi_private_label_bg),
            contentDescription = null,
            tint = Color.Unspecified,
            modifier = Modifier.matchParentSize()
        )

        Text(
            text = "X${count}",
            style = TextStyle(
                fontSize = 10.sp,
                fontWeight = FontWeight.W900,
                fontStyle = FontStyle.Italic,
                color = Color.White
            ),
            modifier = Modifier.padding(horizontal = 8.dp, vertical = 2.dp)
        )
    }
}

private val MEDIA_WIDTH = 182.dp
private val MEDIA_HEIGHT = 242.dp
private val MEDIA_SIZE = DpSize(MEDIA_WIDTH, MEDIA_HEIGHT)
private val MEDIA_SHAPE = RoundedCornerShape(24.dp)

@Preview(showBackground = true)
@Composable
private fun ChatMediaMessageItemPreviewImage() {
    val mine = UserInfo(userId = "1")
    val other = UserInfo(userId = "2")

    val content = ImageMessage.obtain("".toUri())
    val message = Message.obtain("", Conversation.ConversationType.PRIVATE, content)

    ChatMediaMessageItem(
        mine = mine,
        other = other,
        message = message,
        content = content,
        onResendClick = {},
        onProfileClick = {},
        onMediaClick = {},
        onExpired = {},
    )
}

@Preview
@Composable
private fun MultiPrivateLabelPreview() {
    MultiPrivateLabel(3)
}



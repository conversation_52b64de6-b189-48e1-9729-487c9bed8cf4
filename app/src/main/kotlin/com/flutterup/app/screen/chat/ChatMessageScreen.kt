@file:OptIn(ExperimentalMaterial3Api::class, ExperimentalLayoutApi::class)

package com.flutterup.app.screen.chat

import androidx.activity.result.PickVisualMediaRequest
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.imeNestedScroll
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.material3.pulltorefresh.rememberPullToRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppLoadMoreIndicator
import com.flutterup.app.design.extension.rememberPickVisualMediaRequest
import com.flutterup.app.design.haze.DefaultHazeStyle
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.model.AppFrom
import com.flutterup.app.model.AppPaymentFrom
import com.flutterup.app.model.GreetingItem
import com.flutterup.app.screen.LocalAppState
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.chat.component.message.ChatMessageGreeting
import com.flutterup.app.screen.chat.component.message.ChatMessageInput
import com.flutterup.app.screen.chat.component.message.ChatMessageItem
import com.flutterup.app.screen.chat.component.message.ChatMessageLockPrivateBubble
import com.flutterup.app.screen.chat.component.message.ChatMessageNavigation
import com.flutterup.app.screen.chat.component.message.ChatMessageRightNavigation
import com.flutterup.app.screen.chat.component.message.ChatMessageWithoutLimit
import com.flutterup.app.screen.chat.state.ChatMessageUiState
import com.flutterup.app.screen.chat.vm.ChatMessageViewModel
import com.flutterup.app.screen.chat.vm.ConversationType
import com.flutterup.app.screen.profile.ProfileOtherPreRoute
import com.flutterup.base.compose.refresh.RefreshLoadMoreBox
import com.flutterup.base.compose.refresh.rememberPullToLoadMoreState
import com.flutterup.chat.message.content.ConnectBaseMessageContent
import com.flutterup.chat.message.content.PrivateMessageContent
import com.flutterup.gifts.entity.GiftEntity
import com.flutterup.players.compose.PlayerConfig
import com.flutterup.players.compose.SimpleAlphaPlayer
import com.flutterup.players.compose.rememberAlphaPlayerState
import com.ss.ugc.android.alpha_player.model.ScaleType
import dev.chrisbanes.haze.hazeEffect
import dev.chrisbanes.haze.hazeSource
import dev.chrisbanes.haze.rememberHazeState
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message

@Composable
fun ChatMessageScreen(targetId: String) {
    val conversationType = remember(targetId) { ConversationType.Private(targetId) }
    ChatMessageScreen(conversationType)
}

@Composable
fun ChatMessageCustomerServiceScreen() {
    val conversationType = remember { ConversationType.CustomerService }
    ChatMessageScreen(conversationType)
}

@Composable
private fun ChatMessageScreen(conversationType: ConversationType) {
    val navController = LocalNavController.current
    val navCenter = LocalAppState.current.navCenter

    val viewModel = hiltViewModel<ChatMessageViewModel>()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val playerState = rememberAlphaPlayerState()

    LaunchedEffect(conversationType) {
        viewModel.init(conversationType)
    }

    val launcher = rememberPickVisualMediaRequest { uri ->
        if (uri == null) return@rememberPickVisualMediaRequest
        viewModel.sendNormalMomentsMessage(uri)
    }

    ChatMessageContent(
        uiState = uiState,
        onBackClick = navController::popBackStack,
        onProfileClick = {
            val userinfo = uiState.otherUserinfo ?: return@ChatMessageContent

            navController.navigate(ProfileOtherPreRoute(userinfo, AppFrom.Chat))
        },
        onRefresh = {
            viewModel.loadMessageHistory()
        },
        onLoadMore = {}, //不需要加载更多
        onSettingsClick = {

        },
        onInputValueChange = { viewModel.updateInputValue(it) },
        onGreetingSend = { viewModel.sendTextMessage(it.title) },
        onTextSendClick = { viewModel.sendTextMessage() },
        onGiftSendClick = { viewModel.sendGiftMessage(it) },
        onResendClick = { viewModel.resendMessage(it) },
        onMediaClick = { viewModel.openOrPreviewMediaMessage(it) },
        onMediaExpired = { navCenter.popupMediaPreview() },
        onGiftClick = { viewModel.openOrPlayGiftMessage(it) },
        onNormalMomentsClick = { launcher.launch(PickVisualMediaRequest()) },
        onPrivateMomentsClick = { navCenter.navigateToPrivateMoments() },
        onGotoVipClick = { navCenter.navigateToVipDialog(AppPaymentFrom.MAX_CHAT_LIMIT) }
    )



    uiState.currentPlayerGift?.let {
        SimpleAlphaPlayer(
            modifier = Modifier.fillMaxSize(),
            playerState = playerState,
            videoPath = it.videoPath,
            config = PlayerConfig(
                scaleType = ScaleType.ScaleToFill
            ),
            onPlaybackComplete = { viewModel.stopPlayGiftMessage() },
            onError = { viewModel.stopPlayGiftMessage() }
        )
    }
}


@Composable
private fun ChatMessageContent(
    uiState: ChatMessageUiState,
    onLoadMore: () -> Unit = {},
    onRefresh: () -> Unit = {},
    onBackClick: () -> Unit = {},
    onProfileClick: () -> Unit = {},
    onSettingsClick: () -> Unit = {},
    onInputValueChange: (String) -> Unit = {},
    onGreetingSend: (GreetingItem) -> Unit = {},
    onTextSendClick: () -> Unit = {},
    onGiftSendClick: (GiftEntity) -> Unit = {},
    onResendClick: (Message) -> Unit = {},
    onMediaClick: (Message) -> Unit = {},
    onMediaExpired: (Message) -> Unit = {},
    onGiftClick: (Message) -> Unit = {},
    onNormalMomentsClick: () -> Unit = {},
    onPrivateMomentsClick: () -> Unit = {},
    onGotoVipClick: () -> Unit = {},
) {
    val focusManager = LocalFocusManager.current
    val pullToRefreshState = rememberPullToRefreshState()
    val pullToLoadMoreState = rememberPullToLoadMoreState()
    val lazyListState = rememberLazyListState()
    val hazeState = rememberHazeState(true)

    AppScaffold(
        title = { },
        navigation = { ChatMessageNavigation(uiState, onBackClick, onProfileClick) },
        rightNavigationContent = { ChatMessageRightNavigation(onSettingsClick) },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(containerColor = Color.Transparent)
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(brush = BACKGROUND_BRUSH)
        ) {
            Column(
                modifier = Modifier
                    .padding(top = paddingValues.calculateTopPadding())
                    .fillMaxSize()
                    .noRippleClickable(onClick = {
                        focusManager.clearFocus()
                    })
            ) {
                RefreshLoadMoreBox(
                    isRefreshing = uiState.isRefreshing,
                    isLoadingMore = uiState.isLoadingMore,
                    hasNoMoreData = uiState.hasNoMoreData,
                    onRefresh = onRefresh,
                    onLoadMore = onLoadMore,
                    pullRefreshState = pullToRefreshState,
                    pullLoadMoreState = pullToLoadMoreState,
                    lazyListState = lazyListState,
                    loadMoreIndicator = {
                        AppLoadMoreIndicator(
                            isLoadingMore = uiState.isLoadingMore,
                            hasNoMoreData = uiState.hasNoMoreData,
                            state = pullToLoadMoreState
                        )
                    },
                    thresholdDp = 90.dp,
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxWidth()
                ) {
                    LazyColumn(
                        contentPadding = PaddingValues(horizontal = 14.dp, vertical = 20.dp),
                        state = lazyListState,
                        verticalArrangement = Arrangement.spacedBy(20.dp),
                        reverseLayout = true,
                        modifier = Modifier
                            .matchParentSize()
                            .then(if (uiState.isChatWithoutLimit) Modifier else Modifier.imeNestedScroll()),
                    ) {
                        itemsIndexed(
                            items = uiState.messages,
                            key = { index, message -> "${message.messageId}_${message.sentStatus}_${message.sentTime}_${message.uId.orEmpty()}_$index" }
                        ) { index, message ->
                            val previousMessage = uiState.messages.getOrNull(index - 1)

                            ChatMessageItem(
                                mine = uiState.mineUserinfo,
                                other = uiState.otherUserinfo,
                                previousMessage = previousMessage,
                                message = message,
                                onProfileClick = onProfileClick,
                                onResendClick = onResendClick,
                                onMediaClick = onMediaClick,
                                onMediaExpired = onMediaExpired,
                                onGiftClick = onGiftClick,
                            )
                        }
                    }
                }

                if (uiState.greetingEnable) {
                    ChatMessageGreeting(
                        greetings = uiState.greetings,
                        onGreetingClick = onGreetingSend,
                        modifier = Modifier
                            .padding(horizontal = 28.dp)
                            .padding(bottom = 28.dp)
                            .fillMaxWidth()
                    )
                }

                ChatMessageInput(
                    value = uiState.inputValue,
                    enable = !uiState.isChatWithoutLimit, //聊天没有被限制
                    uiState = uiState,
                    onValueChange = onInputValueChange,
                    onSendClick = onTextSendClick,
                    onGiftSendClick = onGiftSendClick,
                    onNormalMomentsClick = onNormalMomentsClick,
                    onPrivateMomentsClick = onPrivateMomentsClick,
                    modifier = Modifier
                        .hazeSource(hazeState)
                )
            }

            if (uiState.lockedPrivateMoments.isNotEmpty()) { //有符合条件的未解锁内容
                ChatMessageLockPrivateBubble(
                    state = lazyListState,
                    messages = uiState.lockedPrivateMoments,
                    parentMessage = uiState.messages,
                    modifier = Modifier
                        .padding(top = paddingValues.calculateTopPadding())
                        .padding(top = 11.dp)
                        .align(Alignment.TopEnd)
                )
            }

            if (uiState.isChatWithoutLimit) { //是否聊天超限制
                ChatMessageWithoutLimit(
                    onClick = onGotoVipClick,
                    modifier = Modifier
                        .hazeEffect(hazeState, DefaultHazeStyle.copy(
                            backgroundColor = Color(0xFFF0E9FF),
                        )) {
                            blurEnabled = true
                            clipToAreasBounds = true
                        }
                        .fillMaxWidth()
                        .padding(bottom = 127.dp)
                        .align(Alignment.BottomCenter)
                )
            }
        }
    }
}

private val BACKGROUND_BRUSH = Brush.verticalGradient(
    colors = listOf(
        Color(0xFFF6F3FC),
        Color(0xFFF0E9FF)
    )
)

@Preview
@Composable
private fun ChatMessageScreenPreview1() {
    val conversation = Conversation.obtain(Conversation.ConversationType.PRIVATE, "1", "111")
    val messages = listOf<Message>(
        Message.obtain("0", conversation.conversationType, ConnectBaseMessageContent()).apply { 
            messageId = 0
        },
        Message.obtain("1", conversation.conversationType, PrivateMessageContent()).apply {
            messageId = 1
            receivedTime = 1757572072641L
            messageDirection = Message.MessageDirection.RECEIVE
            setExpansion(hashMapOf("status" to "0"))
        },
        Message.obtain("2", conversation.conversationType, PrivateMessageContent()).apply {
            messageId = 2
            receivedTime = 1757572072641L
            messageDirection = Message.MessageDirection.RECEIVE
            setExpansion(hashMapOf("status" to "0"))
        }
    ).asReversed()

    AppTheme {
        ChatMessageContent(
            uiState = ChatMessageUiState(
                inputValue = "Default input",
                conversation = conversation,
                messages = messages,
                greetings = List(3) {
                    GreetingItem(
                        title = "testanaskjdnsakjansjksandjksnadksdndjasnadsdknkjnkjasdnk$it",
                        id = it.toLong(),
                    )
                },
                privateMomentsShowMinNum = 1,
                privateMomentsShowDelayTime = 100,
            )
        )
    }
}

@Preview
@Composable
private fun ChatMessageScreenPreview2() {
    val conversation = Conversation.obtain(Conversation.ConversationType.PRIVATE, "1", "111")
    val messages = listOf<Message>(
        Message.obtain("0", conversation.conversationType, ConnectBaseMessageContent()).apply {
            messageId = 0
        },
        Message.obtain("1", conversation.conversationType, PrivateMessageContent()).apply {
            messageId = 1
            receivedTime = 1757572072641L
            messageDirection = Message.MessageDirection.RECEIVE
            setExpansion(hashMapOf("status" to "0"))
        },
        Message.obtain("2", conversation.conversationType, PrivateMessageContent()).apply {
            messageId = 2
            receivedTime = 1757572072641L
            messageDirection = Message.MessageDirection.RECEIVE
            setExpansion(
                hashMapOf("status" to "0", "data" to "111")
            )
        }
    ).asReversed()

    AppTheme {
        ChatMessageContent(
            uiState = ChatMessageUiState(
                inputValue = "Default input",
                conversation = conversation,
                messages = messages,
                greetings = List(3) {
                    GreetingItem(
                        title = "testanaskjdnsakjansjksandjksnadksdndjasnadsdknkjnkjasdnk$it",
                        id = it.toLong(),
                    )
                },
                privateMomentsShowMinNum = 1,
                privateMomentsShowDelayTime = 100,
            )
        )
    }
}
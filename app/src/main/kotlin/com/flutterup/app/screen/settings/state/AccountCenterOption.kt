package com.flutterup.app.screen.settings.state

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import com.flutterup.app.R

enum class AccountCenterOption(
    @StringRes val titleRes: Int,
    @DrawableRes val iconRes: Int?
) {
    CHANGE_PWD(R.string.change_pwd, R.drawable.ic_settings_change_pwd),

//    DELETE_ACCOUNT(R.string.delete_account, R.drawable.ic_settings_delete_account)
}
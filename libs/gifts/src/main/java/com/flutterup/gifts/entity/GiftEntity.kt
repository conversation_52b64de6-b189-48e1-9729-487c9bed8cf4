package com.flutterup.gifts.entity

data class GiftEntity(
    val giftId: String,

    val name: String,

    val price: Int,

    val image: String?,

    val desc: String?,

    val gif: String?,

    val video: String?,

    val imagePath: String?,

    val gifPath: String?,

    val videoPath: String?,

    val version: Long
)

/**
 * 将 GiftCacheEntity 转换为 GiftEntity
 */
fun GiftCacheEntity.toGiftEntity(
    price: Int = 0,
    image: String? = null,
    gif: String? = null,
    video: String? = null
): GiftEntity {
    return GiftEntity(
        giftId = this.giftId,
        name = this.name,
        price = price,
        image = image,
        gif = gif,
        video = video,
        imagePath = this.imagePath,
        gifPath = this.gifPath,
        videoPath = this.videoPath,
        desc = this.desc,
        version = this.version
    )
}

/**
 * 将 GiftResourceInfo 和 GiftCacheEntity 合并为 GiftEntity
 */
fun GiftResourceInfo.toGiftEntity(
    cacheEntity: GiftCacheEntity?,
    price: Int = 0
): GiftEntity {
    return GiftEntity(
        giftId = this.giftId,
        name = this.name,
        price = price,
        image = this.image,
        gif = this.gif,
        video = this.video,
        imagePath = cacheEntity?.imagePath,
        gifPath = cacheEntity?.gifPath,
        videoPath = cacheEntity?.videoPath,
        desc = this.desc,
        version = this.version
    )
}

fun GiftEntity.toGiftResourceInfo(): GiftResourceInfo {
    return GiftResourceInfo(
        giftId = this.giftId,
        name = this.name,
        image = this.image,
        gif = this.gif,
        video = this.video,
        desc = this.desc,
        version = this.version
    )
}
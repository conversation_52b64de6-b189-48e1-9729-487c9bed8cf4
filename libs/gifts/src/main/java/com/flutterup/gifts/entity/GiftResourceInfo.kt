package com.flutterup.gifts.entity

import androidx.annotation.Keep
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass


@Keep
@JsonClass(generateAdapter = true)
data class GiftResourceInfo(

    @<PERSON><PERSON>(name = "gift_id")
    val giftId: String,

    @<PERSON><PERSON>(name = "name")
    val name: String,

    @<PERSON><PERSON>(name = "image")
    val image: String? = null,

    @<PERSON><PERSON>(name = "gif")
    val gif: String? = null,

    @<PERSON><PERSON>(name = "video")
    val video: String? = null,

    @<PERSON><PERSON>(name = "desc")
    val desc: String? = null,

    @<PERSON><PERSON>(name = "version")
    val version: Long = 0L
)

@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.developer

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import coil3.compose.AsyncImage
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.AppTitleText
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.chat.state.ChatGiftUiState
import com.flutterup.app.screen.chat.vm.ChatGiftViewModel
import com.flutterup.app.screen.chat.vm.GiftUIState
import com.flutterup.gifts.entity.GiftEntity
import com.flutterup.players.compose.AlphaPlayerState
import com.flutterup.players.compose.PlayerConfig
import com.flutterup.players.compose.SimpleAlphaPlayer
import com.flutterup.players.compose.rememberAlphaPlayerState
import com.ss.ugc.android.alpha_player.model.ScaleType

@Composable
fun DeveloperGiftScreen() {
    val chatGiftViewModel: ChatGiftViewModel = hiltViewModel()
    val uiState by chatGiftViewModel.uiState.collectAsStateWithLifecycle()
    val playerState = rememberAlphaPlayerState()

    GiftScreen(
        navController = LocalNavController.current,
        state = uiState,
        playerState = playerState,
        onIdleClick = { chatGiftViewModel.preloadGifts() }
    )
}

@Composable
private fun GiftScreen(
    navController: NavController = LocalNavController.current,
    state: ChatGiftUiState = ChatGiftUiState(),
    playerState: AlphaPlayerState = rememberAlphaPlayerState(),
    onIdleClick: () -> Unit = {},
    onErrorClick: () -> Unit = {},
) {
    var currentGiftEntity: GiftEntity? by remember { mutableStateOf(null) }

    AppScaffold(
        title = { AppTitleText(text = "Gift") },
        onBackClick = { navController.popBackStack() },
        colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
            containerColor = Color.Transparent,
            navigationIconContentColor = Color.Black,
            titleContentColor = Color.Black,
        )
    ) {
        Box(
            modifier = Modifier.fillMaxSize().padding(it)
        ) {
            when (val giftUiState = state.giftUiState) {
                is GiftUIState.Idle -> {
                    Text(
                        text = "Click to preload gifts",
                        color = Color.Black,
                        modifier = Modifier.align(Alignment.Center).clickable(onClick = onIdleClick),
                    )
                }
                is GiftUIState.Preloading -> {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .size(100.dp)
                            .align(Alignment.Center),
                        color = Color.Black,
                    )
                }
                is GiftUIState.PartialSuccess -> {
                    Text(
                        text = "Partial success, click to retry",
                        modifier = Modifier.align(Alignment.Center).clickable(onClick = onErrorClick),
                    )
                }
                is GiftUIState.Success -> {
                    // show success
                    GiftSuccessScreen(
                        state = giftUiState,
                        onItemClick = { entity -> currentGiftEntity = entity }
                    )
                }
                is GiftUIState.Error -> {
                    Text(
                        text = "Error, click to retry",
                        modifier = Modifier.align(Alignment.Center).clickable(onClick = onErrorClick),
                    )
                }
            }
        }
    }

    if (currentGiftEntity != null) {

    }

    currentGiftEntity?.let {
        SimpleAlphaPlayer(
            modifier = Modifier.fillMaxSize(),
            playerState = playerState,
            videoPath = it.videoPath,
            config = PlayerConfig(
                scaleType = ScaleType.ScaleToFill
            ),
            onPlaybackComplete = {
                currentGiftEntity = null
            },
            onError = {
                currentGiftEntity = null
            }
        )
    }
}

@Composable
private fun GiftSuccessScreen(
    state: GiftUIState.Success,
    onItemClick: (GiftEntity) -> Unit = {},
) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        modifier = Modifier.fillMaxSize().padding()
    ) {
        items(
            count = state.result.cachedGiftsData.size,
            key = { index -> state.result.cachedGiftsData[index].giftId }
        ) { index ->
            val item = state.result.cachedGiftsData[index]

            //加载本地gif图片
            AsyncImage(
                model = item.gifPath,
                contentDescription = null,
                modifier = Modifier.size(100.dp)
                    .clickable(onClick = { onItemClick(item) })
            )
        }
    }
}

@Preview
@Composable
private fun DevGiftScreenPreview() {
    AppTheme {
        GiftScreen(
            navController = rememberNavController(),
        )
    }
}
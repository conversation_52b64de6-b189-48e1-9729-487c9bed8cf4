package com.flutterup.app.screen

import androidx.lifecycle.viewModelScope
import com.flutterup.app.model.UserInfo
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseViewModel
import com.flutterup.base.utils.Timber
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * MainActivity用的viewmodel
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    private val userMonitor: UserMonitor,
) : BaseViewModel() {

    companion object {
        private const val TAG = "MainViewModel"
        private const val MIN_SPLASH_DURATION = 1000L // 最小启动页显示时间
    }

    private val _uiState = MutableStateFlow<MainActivityUIState>(MainActivityUIState.Idle)
    val uiState: StateFlow<MainActivityUIState> = _uiState.asStateFlow()

    init {
        checkAuthenticationState()
    }

    /**
     * 检查用户认证状态
     */
    private fun checkAuthenticationState() {
        viewModelScope.launch {
            try {
                Timber.d(TAG, "Checking authentication state...")

                // 确保启动页至少显示指定时间
                val startTime = System.currentTimeMillis()

                // 检查用户登录状态
                val isLoggedIn = userMonitor.isLogin
                val userInfo = userMonitor.userInfo

                Timber.d(TAG, "User login status: $isLoggedIn, userInfo: ${userInfo != null}")

                // 计算剩余等待时间
                val elapsedTime = System.currentTimeMillis() - startTime
                val remainingTime = MIN_SPLASH_DURATION - elapsedTime
                if (remainingTime > 0) {
                    delay(remainingTime)
                }

                // 更新UI状态
                _uiState.value = when {
                    isLoggedIn && userInfo != null -> {
                        Timber.d(TAG, "User authenticated successfully")
                        if (userInfo.isInvalid) {
                            MainActivityUIState.UncompletedProfile(userInfo)
                        } else {
                            MainActivityUIState.Success(userInfo)
                        }
                    }
                    else -> {
                        Timber.d(TAG, "User not authenticated")
                        MainActivityUIState.Unauthorized
                    }
                }

            } catch (e: Exception) {
                Timber.e(TAG, "Error checking authentication state", e)
                _uiState.value = MainActivityUIState.Unauthorized
            }
        }
    }
}

sealed interface MainActivityUIState {
    data object Idle : MainActivityUIState

    data object Unauthorized : MainActivityUIState

    data class UncompletedProfile(val userInfo: UserInfo) : MainActivityUIState

    data class Success(val userInfo: UserInfo) : MainActivityUIState


    /**
     * Returns `true` if the state wasn't loaded yet and it should keep showing the splash screen.
     */
    fun shouldKeepSplashScreen() = this is Idle
}
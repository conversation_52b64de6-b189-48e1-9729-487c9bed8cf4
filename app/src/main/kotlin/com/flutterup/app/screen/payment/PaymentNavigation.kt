package com.flutterup.app.screen.payment

import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import androidx.navigation.compose.dialog
import androidx.navigation.toRoute
import com.flutterup.app.model.AppPaymentFrom
import com.flutterup.app.model.PaymentPacksOrderBy
import kotlinx.serialization.Serializable

@Serializable data class PaymentSubscriptionRoute(
    val eventFrom: AppPaymentFrom, //页面来源
    val expireTime: Long? = null, //过期时间
)

@Serializable data class PaymentPacksRoute(
    val orderBy: PaymentPacksOrderBy = PaymentPacksOrderBy.NORMAL, //排序方式
)

@Serializable data class PaymentDiamondsRoute(
    val from: AppPaymentFrom, //页面来源
)

fun NavGraphBuilder.paymentGraph() {
    composable<PaymentSubscriptionRoute> {
        val data = it.toRoute<PaymentSubscriptionRoute>()
        PaymentSubscriptionScreen(
            eventFrom = data.eventFrom,
            expireTime = data.expireTime,
        )
    }

    dialog<PaymentPacksRoute> {
        val data = it.toRoute<PaymentPacksRoute>()
        PaymentPacksScreen(orderBy = data.orderBy)
    }

    dialog<PaymentDiamondsRoute> {
        val data = it.toRoute<PaymentDiamondsRoute>()
        PaymentDiamondsScreen(
            from = data.from,
        )
    }
}
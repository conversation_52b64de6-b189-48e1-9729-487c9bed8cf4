package com.flutterup.app.screen.settings.state

import android.os.Environment
import com.flutterup.app.R
import com.flutterup.base.BaseState

data class SettingsCenterState(
    val options: List<OptionType>,

    val isCleanCaching: Boolean = false,

    override val isLoading: <PERSON>olean = false,
) : BaseState {
    companion object {
        val EMPTY = SettingsCenterState(emptyList())
    }

    enum class OptionType(val titleRes: Int, val iconDrawableRes: Int? = null) {
        ACCOUNT(R.string.account, R.drawable.ic_settings_account),

        NOTIFICATION(R.string.notification, R.drawable.ic_settings_notification),

        APP_ENVIRONMENT(R.string.app_environment),

        DEVELOP_MENU(R.string.develop_menu),
    }
}


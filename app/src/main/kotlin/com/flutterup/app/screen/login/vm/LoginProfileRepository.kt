package com.flutterup.app.screen.login.vm

import com.flutterup.app.model.Gender
import com.flutterup.app.model.UpdateProfileRequest
import com.flutterup.app.network.ApiService
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.zip
import java.time.LocalDate
import javax.inject.Inject

class LoginProfileRepository @Inject constructor(
    private val apiService: ApiService,
    private val userMonitor: UserMonitor
) : BaseRepository() {

    fun updateBaseProfile(
        nickname: String,
        gender: Gender,
        meet: Gender,
        birthday: LocalDate,
    ): Flow<Boolean> {
        val request = UpdateProfileRequest(
            step = UpdateProfileRequest.STEP_ONE,
            sex = gender.value,
            sexuality = meet.value,
            birthday = birthday.toString()
        )
        val request2 = UpdateProfileRequest(
            step = UpdateProfileRequest.STEP_TWO,
            nickName = nickname,
        )

        val flow1 = flow { emit(apiService.updateProfileInfo(request)) }
        val flow2 = flow { emit(apiService.updateProfileInfo(request2)) }

        return flow1.zip(flow2) { result1, result2 ->
            result1.isSuccess && result2.isSuccess
        }
    }
}
@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.common

import androidx.compose.animation.AnimatedContentScope
import androidx.compose.animation.ExperimentalSharedTransitionApi
import androidx.compose.animation.SharedTransitionScope
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import androidx.media3.common.MediaItem
import androidx.media3.exoplayer.ExoPlayer
import coil3.compose.AsyncImage
import com.flutterup.app.design.AppScaffold
import com.flutterup.app.design.component.pager.TextPagerIndicator
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.BackgroundTheme
import com.flutterup.app.design.theme.LocalBackgroundTheme
import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.common.utils.rememberExoPlayersPool
import com.flutterup.app.utils.SharedElementUtils
import com.flutterup.base.compose.zoomable.ZoomState
import com.flutterup.base.compose.zoomable.rememberZoomState
import com.flutterup.base.compose.zoomable.zoomable
import com.flutterup.base.utils.Timber
import com.flutterup.players.media.Media
import com.flutterup.players.media.ResizeMode
import com.flutterup.players.media.ShowBuffering
import com.flutterup.players.media.SimpleController
import com.flutterup.players.media.SurfaceType
import com.flutterup.players.media.rememberMediaState


@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
fun MediaPreviewScreen(
    data: List<MediaItemEntity>,
    initializeIndex: Int = 0,
    sharedTransitionScope: SharedTransitionScope,
    animatedContentScope: AnimatedContentScope
) {
    val navController = LocalNavController.current

    MediaPreviewContent(
        data = data,
        initializeIndex = initializeIndex,
        sharedTransitionScope = sharedTransitionScope,
        animatedContentScope = animatedContentScope,
        onBackClick = navController::popBackStack
    )
}

@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
private fun MediaPreviewContent(
    data: List<MediaItemEntity>,
    initializeIndex: Int,
    onBackClick: () -> Unit = {},
    sharedTransitionScope: SharedTransitionScope?,
    animatedContentScope: AnimatedContentScope?,
) {
    val context = LocalContext.current
    val backgroundTheme = BackgroundTheme(color = Color.Black)
    val pagerState = rememberPagerState(initialPage = initializeIndex) { data.size }
    val zoomState = rememberZoomState()
    val playersPool = rememberExoPlayersPool(data)

    CompositionLocalProvider(
        LocalBackgroundTheme provides backgroundTheme
    ) {
        AppScaffold(
            title = { },
            onBackClick = onBackClick,
            colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                containerColor = Color.Transparent,
                navigationIconContentColor = Color.White,
            ),
            containerColor = backgroundTheme.color
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(bottom = it.calculateBottomPadding())
            ) {
                HorizontalPager(
                    state = pagerState,
                    modifier = Modifier.matchParentSize(),
                    beyondViewportPageCount = 1,
                ) { index ->
                    val item = data[index]

                    if (item.isImage()) {
                        ImagePreviewContent(
                            item = item,
                            zoomState = zoomState,
                            sharedTransitionScope = sharedTransitionScope,
                            animatedContentScope = animatedContentScope
                        )
                    }

                    if (item.isVideo()) {
                        VideoPreviewContent(
                            pagerState = pagerState,
                            player = playersPool.createAndGet(item),
                            video = item,
                            index = index,
                        )
                    }
                }

                TextPagerIndicator(
                    selectedPage = pagerState.currentPage,
                    pageCount = pagerState.pageCount,
                    textStyle = TextStyle(
                        color = Color.White,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W400,
                    ),
                    selectedTextStyle = TextStyle(
                        color = Color.White,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W400,
                    ),
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = 25.dp)
                )
            }
        }
    }
}

@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
private fun ImagePreviewContent(
    item: MediaItemEntity,
    zoomState: ZoomState,
    sharedTransitionScope: SharedTransitionScope?,
    animatedContentScope: AnimatedContentScope?,
) {
    val sharedModifier: Modifier = if (sharedTransitionScope != null && animatedContentScope != null) {
        with(sharedTransitionScope) {
            Modifier
                .sharedElement(
                    sharedContentState = sharedTransitionScope.rememberSharedContentState(SharedElementUtils.generatorMediaUrlKey(item.imageUrl)),
                    animatedVisibilityScope = animatedContentScope
                )
        }
    } else {
        Modifier
    }

    AsyncImage(
        model = item.imageUrl,
        contentDescription = null,
        contentScale = ContentScale.FillWidth,
        modifier = Modifier
            .fillMaxWidth()
            .zoomable(zoomState)
            .then(sharedModifier)
    )
}

@OptIn(ExperimentalSharedTransitionApi::class)
@Composable
private fun VideoPreviewContent(
    pagerState: PagerState,
    player: ExoPlayer,
    video: MediaItemEntity,
    index: Int,
) {
    val isCurrentPage = pagerState.currentPage == index
    val state = rememberMediaState(player)

    LaunchedEffect(video) {
        val mediaItem = MediaItem.Builder()
            .setUri(video.videoUrl.toUri())
            .build()

        state.player?.setMediaItem(mediaItem)
        state.player?.prepare()
    }

    LaunchedEffect(isCurrentPage) {
        if (isCurrentPage) {
            state.player?.play()
        } else {
            state.player?.pause()
        }
    }

    Media(
        state = state,
        modifier = Modifier.fillMaxSize().background(Color.Black),
        surfaceType = SurfaceType.SurfaceView,
        resizeMode = ResizeMode.FixedWidth,
        keepContentOnPlayerReset = false,
        useArtwork = true,
        showBuffering = ShowBuffering.Always,
        buffering = {
            Box(Modifier.fillMaxSize(), Alignment.Center) {
                CircularProgressIndicator()
            }
        },
        overlay = {
            if (!state.isReady) {
                AsyncImage(
                    model = video.thumbUrl,
                    contentDescription = null,
                    contentScale = ContentScale.FillWidth,
                    modifier =
                        Modifier.fillMaxWidth().align(Alignment.Center)
                )
            }
        },
        errorMessage = {
            Timber.e("Media error: ", it)
        }
    ) { state ->
        SimpleController(state, Modifier.fillMaxSize())
    }
}


@OptIn(ExperimentalSharedTransitionApi::class)
@Preview
@Composable
private fun MediaPreviewScreenPreview() {
    AppTheme {
        MediaPreviewContent(
            data = listOf(
                MediaItemEntity(0, ""),
                MediaItemEntity(1, ""),
                MediaItemEntity(0, ""),
                MediaItemEntity(1, ""),
                MediaItemEntity(0, ""),
            ),
            initializeIndex = 2,
            sharedTransitionScope = null,
            animatedContentScope = null
        )
    }
}
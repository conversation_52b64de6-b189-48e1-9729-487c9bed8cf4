package com.flutterup.gifts

import com.flutterup.gifts.database.GiftDatabase
import com.flutterup.gifts.dao.GiftCacheDao
import com.flutterup.gifts.entity.GiftCacheEntity
import com.flutterup.gifts.entity.GiftResourceInfo
import com.flutterup.gifts.entity.GiftResourceType
import com.flutterup.gifts.entity.PreloadProgress
import com.flutterup.gifts.entity.PreloadResult
import java.io.File
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.test.setMain
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertIs
import kotlin.test.assertTrue

@OptIn(ExperimentalCoroutinesApi::class)
class GiftPreloadManagerTest {

    private lateinit var giftPreloadManager: GiftPreloadManager
    private lateinit var mockGiftCacheManager: GiftCacheManager
    private lateinit var mockDatabase: GiftDatabase
    private lateinit var mockGiftCacheDao: GiftCacheDao
    private lateinit var mockFileDir: File
    private val testDispatcher = StandardTestDispatcher()

    @Before
    fun setup() {
        Dispatchers.setMain(testDispatcher)
        mockGiftCacheManager = mockk()
        mockDatabase = mockk()
        mockGiftCacheDao = mockk()
        mockFileDir = mockk()

        coEvery { mockDatabase.giftCacheDao() } returns mockGiftCacheDao
        coEvery { mockGiftCacheDao.getGifts(any()) } returns emptyList()
        coEvery { mockGiftCacheDao.getAllGifts() } returns emptyList()

        giftPreloadManager = GiftPreloadManager(testDispatcher, mockGiftCacheManager, mockDatabase)
    }

    @Test
    fun `preloadGiftsAsync with empty list returns success`() = runTest {
        val result = giftPreloadManager.preloadGiftsAsync(emptyList())
        
        assertIs<PreloadResult.Success>(result)
        assertTrue(result.successfulGifts.isEmpty())
        assertTrue(result.failedGifts.isEmpty())
    }

    @Test
    fun `preloadGiftsAsync with successful caching returns success`() = runTest {
        val gifts = listOf(
            GiftResourceInfo(
                giftId = "gift1",
                name = "Test Gift 1",
                image = "https://example.com/gift1.png",
                version = 1
            ),
            GiftResourceInfo(
                giftId = "gift2",
                name = "Test Gift 2",
                gif = "https://example.com/gift2.gif",
                version = 1
            )
        )

        val mockCachedData = listOf(
            GiftCacheEntity("gift1", "Test Gift 1", "/path/gift1.png", null, null, 1),
            GiftCacheEntity("gift2", "Test Gift 2", null, "/path/gift2.gif", null, 1)
        )

        coEvery { mockGiftCacheManager.cacheGiftIfNeeded(any()) } returns Unit
        coEvery { mockGiftCacheDao.getGifts(listOf("gift1", "gift2")) } returns mockCachedData

        val result = giftPreloadManager.preloadGiftsAsync(gifts)

        assertIs<PreloadResult.Success>(result)
        assertEquals(2, result.successfulGifts.size)
        assertTrue(result.failedGifts.isEmpty())
        assertTrue(result.successfulGifts.contains("gift1"))
        assertTrue(result.successfulGifts.contains("gift2"))
        assertEquals(2, result.cachedGiftsData.size)
        assertEquals("gift1", result.cachedGiftsData[0].giftId)
        assertEquals("gift2", result.cachedGiftsData[1].giftId)
        // 验证 GiftEntity 包含完整信息
        assertEquals("Test Gift 1", result.cachedGiftsData[0].name)
        assertEquals("https://example.com/gift1.png", result.cachedGiftsData[0].image)
        assertEquals("/path/gift1.png", result.cachedGiftsData[0].imagePath)

        coVerify(exactly = 2) { mockGiftCacheManager.cacheGiftIfNeeded(any()) }
        coVerify(exactly = 1) { mockGiftCacheDao.getGifts(listOf("gift1", "gift2")) }
        coVerify(exactly = 1) { mockGiftCacheDao.getAllGifts() }
    }

    @Test
    fun `preloadGiftsAsync with cleanup removes obsolete gifts`() = runTest {
        val currentGifts = listOf(
            GiftResourceInfo(giftId = "gift1", name = "Gift 1", version = 1),
            GiftResourceInfo(giftId = "gift2", name = "Gift 2", version = 1)
        )

        val obsoleteGifts = listOf(
            GiftCacheEntity("gift3", "Old Gift 3", "/path/gift3.png", null, null, 1),
            GiftCacheEntity("gift4", "Old Gift 4", null, "/path/gift4.gif", null, 1)
        )

        val currentCachedData = listOf(
            GiftCacheEntity("gift1", "Gift 1", "/path/gift1.png", null, null, 1),
            GiftCacheEntity("gift2", "Gift 2", null, "/path/gift2.gif", null, 1)
        )

        // Mock database responses
        coEvery { mockGiftCacheDao.getAllGifts() } returns (obsoleteGifts + currentCachedData)
        coEvery { mockGiftCacheDao.deleteGifts(listOf("gift3", "gift4")) } returns Unit
        coEvery { mockGiftCacheManager.cacheGiftIfNeeded(any()) } returns Unit
        coEvery { mockGiftCacheDao.getGifts(listOf("gift1", "gift2")) } returns currentCachedData

        val result = giftPreloadManager.preloadGiftsAsync(currentGifts, cleanupObsolete = true)

        assertIs<PreloadResult.Success>(result)
        assertEquals(2, result.successfulGifts.size)
        assertTrue(result.failedGifts.isEmpty())

        // Verify cleanup was called
        coVerify(exactly = 1) { mockGiftCacheDao.getAllGifts() }
        coVerify(exactly = 1) { mockGiftCacheDao.deleteGifts(listOf("gift3", "gift4")) }
        coVerify(exactly = 2) { mockGiftCacheManager.cacheGiftIfNeeded(any()) }
    }

    @Test
    fun `preloadGiftsAsync with cleanup disabled does not remove obsolete gifts`() = runTest {
        val currentGifts = listOf(
            GiftResourceInfo(giftId = "gift1", name = "Gift 1", version = 1)
        )

        coEvery { mockGiftCacheManager.cacheGiftIfNeeded(any()) } returns Unit
        coEvery { mockGiftCacheDao.getGifts(listOf("gift1")) } returns listOf(
            GiftCacheEntity("gift1", "Gift 1", "/path/gift1.png", null, null, 1)
        )

        val result = giftPreloadManager.preloadGiftsAsync(currentGifts, cleanupObsolete = false)

        assertIs<PreloadResult.Success>(result)
        assertEquals(1, result.successfulGifts.size)

        // Verify cleanup was NOT called
        coVerify(exactly = 0) { mockGiftCacheDao.getAllGifts() }
        coVerify(exactly = 0) { mockGiftCacheDao.deleteGifts(any()) }
    }

    @Test
    fun `preloadGiftsAsync with partial failure returns partial success`() = runTest {
        val gifts = listOf(
            GiftResourceInfo(giftId = "gift1", name = "Gift 1", version = 1),
            GiftResourceInfo(giftId = "gift2", name = "Gift 2", version = 1)
        )

        coEvery { mockGiftCacheManager.cacheGiftIfNeeded(match { it.giftId == "gift1" }) } returns Unit
        coEvery { mockGiftCacheManager.cacheGiftIfNeeded(match { it.giftId == "gift2" }) } throws Exception("Network error")

        val result = giftPreloadManager.preloadGiftsAsync(gifts)

        assertIs<PreloadResult.PartialSuccess>(result)
        assertEquals(1, result.successfulGifts.size)
        assertEquals(1, result.failedGifts.size)
        assertTrue(result.successfulGifts.contains("gift1"))
        assertTrue(result.failedGifts.contains("gift2"))
    }

    @Test
    fun `preloadProgress flow updates correctly`() = runTest {
        val initialProgress = giftPreloadManager.preloadProgress.first()
        assertIs<PreloadProgress.Idle>(initialProgress)
    }

    @Test
    fun `isPreloading returns false initially`() {
        val isPreloading = giftPreloadManager.isPreloading()
        assertEquals(false, isPreloading)
    }

    @Test
    fun `cancelPreloading sets progress to idle`() = runTest {
        giftPreloadManager.cancelPreloading()
        val progress = giftPreloadManager.preloadProgress.first()
        assertIs<PreloadProgress.Idle>(progress)
    }

    @Test
    fun `preloadSpecificResources filters gifts correctly`() = runTest {
        val gifts = listOf(
            GiftResourceInfo(giftId = "gift1", name = "Gift 1", image = "url1", version = 1),
            GiftResourceInfo(giftId = "gift2", name = "Gift 2", gif = "url2", version = 1),
            GiftResourceInfo(giftId = "gift3", name = "Gift 3", video = "url3", version = 1)
        )

        coEvery { mockGiftCacheManager.cacheGiftIfNeeded(any()) } returns Unit

        var completedResult: PreloadResult? = null
        giftPreloadManager.preloadSpecificResources(
            gifts = gifts,
            resourceTypes = setOf(GiftResourceType.IMAGE, GiftResourceType.GIF),
            onComplete = { result -> completedResult = result }
        )

        testDispatcher.scheduler.advanceUntilIdle()

        assertIs<PreloadResult.Success>(completedResult)
        assertEquals(2, completedResult!!.successfulGifts.size)
        assertTrue(completedResult!!.successfulGifts.contains("gift1"))
        assertTrue(completedResult!!.successfulGifts.contains("gift2"))
    }
}

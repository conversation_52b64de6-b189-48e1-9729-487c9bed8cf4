package com.flutterup.app.screen.chat.vm

import android.content.Context
import androidx.annotation.Keep
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.flutterup.app.screen.chat.state.ChatGiftUiState
import com.flutterup.base.utils.JsonUtils
import com.flutterup.base.utils.Timber
import com.flutterup.gifts.GiftPreloadManager
import com.flutterup.gifts.entity.GiftEntity
import com.flutterup.gifts.entity.GiftResourceInfo
import com.flutterup.gifts.entity.PreloadProgress
import com.flutterup.gifts.entity.PreloadResult
import com.squareup.moshi.JsonClass
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class ChatGiftViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val giftPreloadManager: GiftPreloadManager,
    private val jsonUtils: JsonUtils,
) : ViewModel() {

    private var data: Gifts? = null

    private val _uiState = MutableStateFlow(ChatGiftUiState())

    private val giftUiState: Flow<GiftUIState> = giftPreloadManager.preloadProgress
        .map { progress ->
            when (progress) {
                is PreloadProgress.Idle -> GiftUIState.Idle
                is PreloadProgress.InProgress -> GiftUIState.Preloading(progress)
                is PreloadProgress.Completed -> when (val result = progress.result) {
                    is PreloadResult.Success -> GiftUIState.Success(result)
                    is PreloadResult.PartialSuccess -> GiftUIState.PartialSuccess
                    is PreloadResult.Error -> GiftUIState.Error
                }
            }
        }


    val uiState: StateFlow<ChatGiftUiState> = combine(
        _uiState,
        giftUiState,
    ) { ui, giftUiState ->
        ui.copy(giftUiState = giftUiState)
    }.stateIn(
        viewModelScope,
        SharingStarted.Eagerly,
        _uiState.value
    )

    init {
        data = loadGiftsFromAssets()
    }

    fun preloadGifts() {
        if (giftPreloadManager.isPreloading()) return //防止重复加载

        if (data == null) {
            data = loadGiftsFromAssets()
        }

        data?.gifts?.let {
            giftPreloadManager.preloadGifts(it)
        }
    }

    private fun loadGiftsFromAssets(): Gifts? {
        return context.assets.open("gift.json").bufferedReader().use { it.readText() }
            .let { jsonUtils.fromJson(json = it, clazz = Gifts::class.java) }
    }

    fun updateCurrentGift(gift: GiftEntity) {
        _uiState.update { it.copy(currentGift = gift) }
    }

    @Keep
    @JsonClass(generateAdapter = true)
    data class Gifts(
        val gifts: List<GiftResourceInfo>
    )
}

sealed interface GiftUIState {
    data object Idle : GiftUIState

    data class Preloading(val progress: PreloadProgress.InProgress) : GiftUIState

    data object PartialSuccess : GiftUIState

    data class Success(val result: PreloadResult.Success) : GiftUIState

    data object Error : GiftUIState
}
package com.flutterup.app.navigation

import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.navigation.NavDestination.Companion.hasRoute
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.NavOptions
import androidx.navigation.Navigator
import androidx.navigation.navOptions
import androidx.navigation.toRoute
import com.flutterup.app.model.AppPaymentFrom
import com.flutterup.app.model.SystemMessageEntity
import com.flutterup.app.model.UserInfo
import com.flutterup.app.utils.UserMonitor
import com.flutterup.app.screen.HomeBaseRoute
import com.flutterup.app.screen.MainActivity
import com.flutterup.app.screen.chat.ChatPrivateMomentsRoute
import com.flutterup.app.screen.chat.ChatPrivateMomentsTutorialRoute
import com.flutterup.app.screen.common.MediaPreviewRoute
import com.flutterup.app.screen.login.LoginBaseRoute
import com.flutterup.app.screen.login.LoginProfileStep1Route
import com.flutterup.app.screen.login.LoginProfileStep2Route
import com.flutterup.app.screen.payment.PaymentDiamondsRoute
import com.flutterup.app.screen.payment.PaymentSubscriptionRoute
import com.flutterup.base.store.MMKVStore
import com.flutterup.base.store.booleanValue
import com.flutterup.base.store.nullableBooleanValue
import com.flutterup.base.utils.Timber
import com.flutterup.network.Action
import dagger.hilt.android.qualifiers.ApplicationContext
import io.rong.imlib.model.Message
import javax.inject.Inject
import javax.inject.Singleton

private const val TAG = "GlobalNavCenter"

/**
 * 全局导航中心
 * 用于管理全局导航相关逻辑
 */
@Singleton
class GlobalNavCenter @Inject constructor(
    private val userMonitor: UserMonitor,
    private val mmkvStore: MMKVStore,
    @ApplicationContext private val context: Context
) {
    private var navController: NavHostController? = null

    private var isFirstOpenPrivateMomentTutorial: Boolean by mmkvStore.booleanValue("isFirstOpenPrivateMomentTutorial", true)

    fun setNavController(navController: NavHostController) {
        this.navController = navController
    }

    fun popupMediaPreview() {
        val backStackEntry = navController?.currentBackStackEntry ?: return
        val destination = backStackEntry.destination

        if (destination.hasRoute(MediaPreviewRoute::class)) {
            navController?.popBackStack(MediaPreviewRoute::class, inclusive = true, saveState = false)
        }
    }

    fun navigateLogin() {
        navController?.navigate(LoginBaseRoute)
    }

    /**
     * 导航到首页, 会检查是是无效用户
     */
    fun navigateHome() {
        if (userMonitor.isInvalid) { //是无效用户, 跳转资料填写页
            navigateToLoginProfileStep1()
            return
        }

        navController?.navigate(HomeBaseRoute)
    }

    fun navigateToLoginProfileStep1() {
        navController?.navigate(LoginProfileStep1Route)
    }

    fun navigateToLoginProfileStep2() {
        navController?.navigate(LoginProfileStep2Route)
    }


    /**
     * UI logic for navigating to a top level destination in the app. Top level destinations have
     * only one copy of the destination of the back stack, and save and restore state whenever you
     * navigate to and from it.
     *
     * @param topLevelDestination: The destination the app needs to navigate to.
     */
    fun navigateToTopLevelDestination(topLevelDestination: TopLevelDestination) {
        navController?.let {
            val topLevelNavOptions = navOptions {
                // 弹出到图表的起始目标，避免在返回栈中堆积大量目标页面
                popUpTo(it.graph.findStartDestination().id) {
                    saveState = true
                }
                // 避免重新选择同一项目时产生多个相同目标的副本
                launchSingleTop = true
                // 重新选择之前选择的项目时恢复状态
                restoreState = true
            }

            it.navigate(topLevelDestination.route, topLevelNavOptions)
        }
    }

    fun navigateToTopLevelDestination(route: BottomNavRoute) {
        navController?.let {
            val topLevelNavOptions = navOptions {
                // 弹出到图表的起始目标，避免在返回栈中堆积大量目标页面
                popUpTo(it.graph.findStartDestination().id) {
                    saveState = true
                }
                // 避免重新选择同一项目时产生多个相同目标的副本
                launchSingleTop = true
                // 重新选择之前选择的项目时恢复状态
                restoreState = false //不恢复
            }

            it.navigate(route, topLevelNavOptions)
        }
    }

    fun navigateToVipDialog(
        from: AppPaymentFrom = AppPaymentFrom.UNKNOWN,
        expireTime: Long? = null,
    ) {
        navController?.navigate(PaymentSubscriptionRoute(from, expireTime))
    }

    fun navigateToPaymentDiamonds(
        from: AppPaymentFrom = AppPaymentFrom.UNKNOWN,
    ) {
        val isVip = userMonitor.isVip

        if (!isVip) { //非会先强制买会员
            navigateToVipDialog(from)
            return
        }
        //会员才允许买钻石
        navController?.navigate(PaymentDiamondsRoute(from))
    }

    fun navigateToPrivateMoments() {
        val isVip = userMonitor.isVip
        if (!isVip) { //非会先强制买会员
            navigateToVipDialog(AppPaymentFrom.CHAT_ALBUM_LIMIT)
            return
        }

        if (isFirstOpenPrivateMomentTutorial) { //第一次打开, 先展示教程
            isFirstOpenPrivateMomentTutorial = false
            navController?.navigate(ChatPrivateMomentsTutorialRoute)
        } else {
            navController?.navigate(ChatPrivateMomentsRoute)
        }
    }

    fun navigateToMediaPreview(route: MediaPreviewRoute) {
        navController?.navigate(route)
    }

    fun navigate(action: Action) {
        Timber.i(TAG, "navigate action: $action")
    }

    fun navigate(systemMessageEntity: SystemMessageEntity) {
        Timber.i(TAG, "navigate systemMessageEntity: $systemMessageEntity")
    }

    fun createPendingIntent(requestCode: Int, systemMessageEntity: SystemMessageEntity): PendingIntent {
        val intent = Intent(context, MainActivity::class.java)
        intent.putExtra(GlobalNavConst.KEY_ACTION, systemMessageEntity.action)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        return PendingIntent.getActivity(context, requestCode, intent, PendingIntent.FLAG_IMMUTABLE)
    }

    fun createMessagePendingIntent(requestCode: Int, message: Message): PendingIntent {
        val action = Action(
            id = GlobalNavConst.ACTION_CHAT,
            params = UserInfo(userId = message.targetId),
        )

        val intent = Intent(context, MainActivity::class.java)
        intent.putExtra(GlobalNavConst.KEY_ACTION, action)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        return PendingIntent.getActivity(context, requestCode, intent, PendingIntent.FLAG_IMMUTABLE)
    }
}
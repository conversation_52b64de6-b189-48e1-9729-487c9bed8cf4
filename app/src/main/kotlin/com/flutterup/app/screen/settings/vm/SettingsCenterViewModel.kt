package com.flutterup.app.screen.settings.vm

import com.flutterup.app.network.environment.AppEnvironmentProvider
import com.flutterup.app.network.environment.DebugEnvironmentProvider
import com.flutterup.app.network.environment.DebugNoSubAccountEnvironmentProvider
import com.flutterup.app.network.environment.ProdEnvironmentProvider
import com.flutterup.app.network.environment.ProdNoSubAccountEnvironmentProvider
import com.flutterup.app.screen.settings.state.SettingsCenterState
import com.flutterup.app.screen.settings.state.SettingsCenterState.OptionType
import com.flutterup.app.utils.UserMonitor
import com.flutterup.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import javax.inject.Inject

@HiltViewModel
class SettingsCenterViewModel @Inject constructor(
    environmentProvider: AppEnvironmentProvider,
    private val userMonitor: UserMonitor
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(SettingsCenterState.EMPTY)
    val uiState: MutableStateFlow<SettingsCenterState> = _uiState

    init {
        when(environmentProvider) {
            is DebugNoSubAccountEnvironmentProvider, is DebugEnvironmentProvider -> {
                _uiState.update { it.copy(options = OptionType.entries) }
            }
            is ProdNoSubAccountEnvironmentProvider, is ProdEnvironmentProvider -> {
                _uiState.update {
                    it.copy(
                        options = listOf(OptionType.ACCOUNT, OptionType.NOTIFICATION)
                    )
                }
            }
        }
    }

    fun logout() {
        _uiState.update { it.copy(isLoading = true) }
        userMonitor.logout()
    }
}
@file:OptIn(ExperimentalMaterial3Api::class)

package com.flutterup.app.screen.payment

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.res.imageResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.flutterup.app.R
import com.flutterup.app.design.component.AppBottomSheetDialog
import com.flutterup.app.design.component.AppContinueButton
import com.flutterup.app.design.component.AppNumberPicker
import com.flutterup.app.design.insets.safeArea
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.AppTheme
import com.flutterup.app.design.theme.AppVipButtonPrimaryColors
import com.flutterup.app.design.theme.PaymentBackgroundGradientEnd
import com.flutterup.app.design.theme.PaymentBackgroundGradientStart
import com.flutterup.app.design.theme.ShadowAmbientPrimary
import com.flutterup.app.design.theme.ShadowSpotPrimary
import com.flutterup.app.design.theme.TextBrownVipPrimary
import com.flutterup.app.design.theme.TextGray999
import com.flutterup.app.model.PaymentPacksItem
import com.flutterup.app.model.PaymentPacksOrderBy
import com.flutterup.app.screen.LocalNavController
import com.flutterup.app.screen.payment.state.PacksColors
import com.flutterup.app.screen.payment.state.PaymentPacksType
import com.flutterup.app.screen.payment.state.PaymentPacksUiState
import com.flutterup.app.screen.payment.vm.PaymentPacksViewModel
import com.flutterup.base.utils.toDecimalSubstring

@Composable
fun PaymentPacksScreen(
    orderBy: PaymentPacksOrderBy,
) {
    val navController = LocalNavController.current
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    val viewModel: PaymentPacksViewModel = hiltViewModel()
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()

    LaunchedEffect(orderBy) {
        viewModel.init(orderBy)
    }

    AppBottomSheetDialog(
        isShown = true,
        onDismissRequest = navController::popBackStack,
        onConfirmRequest = {},
        sheetState = sheetState,
        cancelText = null,
        confirmText = null,
        modifier = Modifier.windowInsetsPadding(WindowInsets.safeArea)
    ) {
        PaymentPacksContent(
            uiState = uiState,
            onValueChange = { viewModel.updateCurrentExchangeQuantity(it) },
            onItemClick = { viewModel.updateCurrentProductId(it.id) },
            onContinueClick = { viewModel.exchange() }
        )
    }
}

@Composable
private fun PaymentPacksContent(
    uiState: PaymentPacksUiState,
    modifier: Modifier = Modifier,
    onValueChange: (Int?) -> Unit = {},
    onItemClick: (PaymentPacksItem) -> Unit = {},
    onContinueClick: () -> Unit = {},
) {
    var boxHeightPx by remember { mutableIntStateOf(0) }

    val backgroundBrush = Brush.verticalGradient(
        colors = listOf(
            PaymentBackgroundGradientStart,
            PaymentBackgroundGradientEnd
        ),
        startY = 0f,
        endY = boxHeightPx * 0.4f //end为整个页面的40%
    )

    val overlayBitmap: ImageBitmap = ImageBitmap.imageResource(id = R.mipmap.ic_packs_top_lights)

    Box(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp))
            .background(backgroundBrush)
            .drawWithContent {
                drawContent()

                drawImage(
                    image = overlayBitmap,
                    dstSize = IntSize(
                        width = size.width.toInt(),
                        height = with(density) { 210.dp.toPx() }.toInt()
                    ),
                    blendMode = BlendMode.Overlay,
                )
            }
            .onSizeChanged {
                boxHeightPx = it.height
            }
    ) {
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 17.dp, end = 12.dp, top = 20.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = stringResource(R.string.diamond_balance),
                    style = TextStyle(
                        fontSize = 14.sp,
                        lineHeight = 22.sp,
                        fontWeight = FontWeight.W400,
                        color = TextBrownVipPrimary,
                    )
                )

                if (uiState.neededDiamonds != null) {
                    Text(
                        text = stringResource(
                            R.string.packs_needed_diamonds,
                            uiState.neededDiamonds
                        ),
                        style = TextStyle(
                            fontSize = 12.sp,
                            lineHeight = 22.sp,
                            fontWeight = FontWeight.W400,
                            color = TextBrownVipPrimary,
                        )
                    )
                }
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 17.dp, end = 12.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = uiState.diamonds.toDecimalSubstring(isReduce = false),
                    style = TextStyle(
                        fontSize = 25.sp,
                        lineHeight = 22.sp,
                        fontWeight = FontWeight.W900,
                        color = TextBrownVipPrimary,
                    )
                )

                AppNumberPicker(
                    value = uiState.currentExchangeQuantity,
                    onValueChange = onValueChange,
                    fieldEnable = uiState.currentProduct != null,
                    negativeEnable = uiState.negativeEnable,
                    positiveEnable = uiState.positiveEnable,
                    modifier = Modifier.size(90.dp, 26.dp)
                )
            }


            PaymentPacksList(
                currentProductId = uiState.currentProductId,
                products = uiState.orderedProducts,
                onItemClick = onItemClick,
            )

            if (uiState.description != null) {
                Text(
                    text = uiState.description,
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.W400,
                        color = TextGray999,
                        textAlign = TextAlign.Justify,
                    ),
                    modifier = Modifier.fillMaxWidth()
                        .padding(top = 10.dp)
                        .padding(horizontal = 20.dp)
                )
            }

            AppContinueButton(
                onClick = onContinueClick,
                enabled = uiState.currentProduct != null, //选中了商品才能点击
                isLoading = uiState.isLoading,
                colors = AppVipButtonPrimaryColors,
                modifier = Modifier
                    .padding(top = 10.dp)
                    .padding(horizontal = 24.dp)
                    .fillMaxWidth()
                    .height(50.dp)
            )
        }
    }
}

@Composable
private fun PaymentPacksList(
    currentProductId: String?,
    products: List<Pair<PaymentPacksType, List<PaymentPacksItem>>>,
    onItemClick: (PaymentPacksItem) -> Unit,
) {
    val packsCardCornerSize = 10.dp

    val topCardShape = RoundedCornerShape(12.dp)
    val packsCardShape = RoundedCornerShape(packsCardCornerSize)
    val overlayBitmap: ImageBitmap = ImageBitmap.imageResource(id = R.mipmap.ic_packs_card_lights)


    LazyColumn(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 11.dp, start = 12.dp, end = 12.dp),
        verticalArrangement = Arrangement.spacedBy(10.dp)
    ) {
        items(products) { typeProducts ->
            PaymentPacksTypeItem(
                currentProductId = currentProductId,
                products = typeProducts,
                packsCardCornerSize = packsCardCornerSize,
                topCardShape = topCardShape,
                packsCardShape = packsCardShape,
                overlayBitmap = overlayBitmap,
                onItemClick = onItemClick,
            )
        }
    }
}

@Composable
private fun PaymentPacksTypeItem(
    currentProductId: String?,
    products: Pair<PaymentPacksType, List<PaymentPacksItem>>,
    packsCardCornerSize: Dp,
    topCardShape: Shape,
    packsCardShape: Shape,
    overlayBitmap: ImageBitmap,
    onItemClick: (PaymentPacksItem) -> Unit,
) {
    val (type, items) = products
    if (type == PaymentPacksType.Unknown || items.isEmpty()) {
        return
    }

    val selectedModifier = Modifier.border(
        width = 1.dp,
        color = type.colors.outlineColor,
        shape = packsCardShape
    )

    Card(
        modifier = Modifier
            .shadow(
                elevation = 5.dp,
                spotColor = ShadowSpotPrimary,
                ambientColor = ShadowAmbientPrimary,
                shape = topCardShape
            )
            .padding(1.dp)
            .fillMaxWidth(),
        shape = topCardShape,
        colors = CardDefaults.cardColors(containerColor = type.colors.topCardBackground)
    ) {
        Row(
            modifier = Modifier.padding(start = 15.dp, end = 15.dp, top = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(5.dp)
        ) {
            Icon(
                imageVector = type.icon,
                contentDescription = null,
                tint = Color.Unspecified,
            )

            Text(
                text = stringResource(type.titleResources),
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight.W400,
                    color = type.colors.textColor,
                )
            )
        }

        Row(
            modifier = Modifier
                .padding(top = 5.dp)
                .background(
                    color = Color.White,
                    shape = topCardShape
                )
                .padding(10.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(10.dp)
        ) {
            for (item in items) {
                PaymentPacksItemCard(
                    selectedModifier = selectedModifier,
                    item = item,
                    type = type,
                    packsCardCornerSize = packsCardCornerSize,
                    packsCardShape = packsCardShape,
                    colors = type.colors,
                    selected = currentProductId == item.id,
                    overlayBitmap = overlayBitmap,
                    onItemClick = onItemClick,
                )
            }
        }
    }
}

@Composable
private fun RowScope.PaymentPacksItemCard(
    modifier: Modifier = Modifier,
    selectedModifier: Modifier,
    item: PaymentPacksItem,
    type: PaymentPacksType,
    packsCardCornerSize: Dp,
    packsCardShape: Shape,
    colors: PacksColors,
    selected: Boolean,
    overlayBitmap: ImageBitmap,
    onItemClick: (PaymentPacksItem) -> Unit,
) {
    Box(
        modifier = modifier
            .height(60.dp)
            .weight(1f)
            .clip(packsCardShape)
            .background(
                brush = colors.packsBackground,
                shape = packsCardShape
            )
            .drawWithContent {
                drawContent()

                drawImage(
                    overlayBitmap,
                    dstSize = IntSize(
                        width = size.width.toInt(),
                        height = size.height.toInt()
                    ),
                    blendMode = BlendMode.Overlay
                )
            }
            .then(if (selected) selectedModifier else Modifier)
            .noRippleClickable { onItemClick(item) }
    ) {
        if (selected) {
            Box(
                modifier = Modifier
                    .size(20.dp, 14.dp)
                    .clip(
                        RoundedCornerShape(
                            topEnd = packsCardCornerSize,
                            bottomStart = packsCardCornerSize
                        )
                    )
                    .background(colors.outlineColor)
                    .align(Alignment.TopEnd),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_packs_selected),
                    contentDescription = null,
                    tint = colors.labelColor,
                    modifier = Modifier
                )
            }
        }

        Column(
            modifier = Modifier.matchParentSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = item.num.toString(),
                style = TextStyle(
                    fontSize = 28.sp,
                    lineHeight = 14.sp,
                    fontWeight = FontWeight.W700,
                    color = type.colors.textColor,
                )
            )

            Text(
                text = stringResource(R.string.times),
                style = TextStyle(
                    fontSize = 12.sp,
                    lineHeight = 14.sp,
                    fontWeight = FontWeight.W400,
                    color = type.colors.textColor,
                )
            )
        }
    }
}

@Preview
@Composable
private fun PaymentPacksScreenPreview() {
    AppTheme {
        Box(
            Modifier
                .fillMaxSize()
                .background(Color.White),
            contentAlignment = Alignment.BottomCenter
        ) {
            PaymentPacksContent(
                uiState = PaymentPacksUiState.TEST,
            )
        }
    }
}
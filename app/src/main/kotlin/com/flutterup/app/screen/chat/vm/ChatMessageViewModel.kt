package com.flutterup.app.screen.chat.vm

import android.net.Uri
import com.flutterup.app.model.MediaItemEntity
import com.flutterup.app.model.MediaMessageEntity
import com.flutterup.app.model.MultiMediaMessageEntity
import com.flutterup.app.navigation.GlobalNavCenter
import com.flutterup.app.screen.chat.state.ChatMessageUiState
import com.flutterup.app.screen.common.MediaPreviewRoute
import com.flutterup.app.utils.GlobalSettingsMonitor
import com.flutterup.app.utils.UserMonitor
import com.flutterup.app.utils.chat.ChatUserInfoCache
import com.flutterup.app.utils.chat.core.ChatMessageExpansionReceiverMonitor
import com.flutterup.app.utils.chat.core.ChatMessageMonitor
import com.flutterup.app.utils.chat.core.ChatOnReceivedMessageListener
import com.flutterup.app.utils.chat.core.ChatRecallMonitor
import com.flutterup.app.utils.chat.core.OnMessageExpansionChangedListener
import com.flutterup.app.utils.chat.core.OnRecallMessageListener
import com.flutterup.app.utils.chat.message.PrivateMessageLockStatus
import com.flutterup.app.utils.chat.message.UnwrapStatus
import com.flutterup.app.utils.chat.message.getMediaList
import com.flutterup.app.utils.chat.message.lockStatus
import com.flutterup.app.utils.chat.message.unwrapStatus
import com.flutterup.base.BaseViewModel
import com.flutterup.base.utils.Timber
import com.flutterup.chat.message.belong
import com.flutterup.chat.message.content.BaseCustomMessageContent
import com.flutterup.chat.message.content.GiftMessageContent
import com.flutterup.chat.message.content.MultiPrivateMessageContent
import com.flutterup.chat.message.content.PrivateMessageContent
import com.flutterup.chat.message.content.PublicMessageContent
import com.flutterup.gifts.GiftPreloadManager
import com.flutterup.gifts.entity.GiftEntity
import com.flutterup.gifts.entity.GiftResourceInfo
import dagger.hilt.android.lifecycle.HiltViewModel
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.HistoryMessageOption
import io.rong.imlib.model.Message
import io.rong.imlib.model.ReceivedProfile
import io.rong.message.ImageMessage
import io.rong.message.MediaMessageContent
import io.rong.message.RecallNotificationMessage
import io.rong.message.TextMessage
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ChatMessageViewModel
@Inject constructor(
    private val navCenter: GlobalNavCenter,
    private val giftPreloadManager: GiftPreloadManager,
    private val userMonitor: UserMonitor,
    private val settingsMonitor: GlobalSettingsMonitor,
    private val messageMonitor: ChatMessageMonitor,
    private val messageExpansionReceiverMonitor: ChatMessageExpansionReceiverMonitor,
    private val messageRecallMonitor: ChatRecallMonitor,
    private val chatConversationRepository: ChatConversationRepository,
    private val chatMessageRepository: ChatMessageRepository,
    private val chatCustomerServiceRepository: ChatCustomerServiceRepository,
) :
    BaseViewModel(),
    ChatOnReceivedMessageListener,
    OnMessageExpansionChangedListener,
    OnRecallMessageListener
{
    private val _uiState = MutableStateFlow(ChatMessageUiState())

    val uiState = combine(
        _uiState,
        userMonitor.userInfoState,
        settingsMonitor.greetingList,
        settingsMonitor.privateShowMinNum,
        settingsMonitor.privateShowDelayTime
    ) { ui, userinfo, greetings, privateShowMinNum, privateShowDelayTime ->
        ui.copy(
            mineUserinfo = userinfo,
            greetings = greetings ?: emptyList(),
            privateMomentsShowMinNum = privateShowMinNum ?: 0,
            privateMomentsShowDelayTime = privateShowDelayTime ?: 0,
        )
    }.distinctUntilChanged()
        .stateIn(
            scope,
            SharingStarted.Eagerly,
            _uiState.value
        )

    init {
        messageMonitor.addMessageListener(this)
        messageExpansionReceiverMonitor.addListener(this)
        messageRecallMonitor.addListener(this)
    }

    override fun onReceivedMessage(message: Message?, profile: ReceivedProfile?) {
        addOrUpdateMessage(message)
    }

    override fun onMessageExpansionUpdate(expansion: Map<String?, String?>?, message: Message?) {
        addOrUpdateMessage(message)
    }

    override fun onMessageRecalled(
        message: Message?,
        recallNotificationMessage: RecallNotificationMessage?
    ) {
        addOrUpdateMessage(message)
    }

    private fun addOrUpdateMessage(message: Message?) {
        val conversation = uiState.value.conversation ?: return
        if (message == null) return

        if (message.belong(conversation)) {
            val messages = _uiState.value.messages.toMutableList()
            val index = messages.indexOfFirst { it.messageId == message.messageId }

            if (index == -1) {
                messages.add(0, message) //因为列表反向了
            } else {
                messages[index] = message
            }
            _uiState.update { it.copy(messages = messages) }

            scope.launch {
                markAllAsRead(conversation)
            }
        }
    }

    fun init(conversationType: ConversationType) {
        when (conversationType) {
            is ConversationType.Private -> initPrivateConversation(conversationType.targetId)
            is ConversationType.CustomerService -> initCustomerService()
        }
    }

    fun loadMessageHistory() {
        scope.launchWithLoading(
            onLoadingChange = { isLoading -> _uiState.update { it.copy(isRefreshing = isLoading) } },
            block = {
                val conversation = uiState.value.conversation ?: return@launchWithLoading
                getMessages(conversation, HistoryMessageDirection.FRONT)
            }
        )
    }

    /**
     * 客服聊天初始化
     */
    private fun initCustomerService() {
        scope.launch {
            val customerServiceAccount = settingsMonitor.customerServiceAccount.value
                ?: settingsMonitor.customerServiceAccount.first { it != null }
                ?: return@launch


            val result = runCatching {
                chatConversationRepository.getConversation(customerServiceAccount, Conversation.ConversationType.PRIVATE)
            }

            var conversation = result.getOrNull()

            if (conversation == null) { //用户和客服没有会话
                chatCustomerServiceRepository.triggerCustomerServiceGreeting(customerServiceAccount)
                conversation = Conversation.obtain(Conversation.ConversationType.PRIVATE, customerServiceAccount, "")
            }

            val otherUserinfo = ChatUserInfoCache.getOrFetchUserInfo(customerServiceAccount)

            _uiState.update {
                it.copy(
                    conversation = conversation,
                    otherUserinfo = otherUserinfo,
                    isCustomerServiceConversation = true,
                )
            }

            getMessages(conversation)
            markAllAsRead(conversation)
        }
    }

    /**
     * 初始化正常私聊
     */
    private fun initPrivateConversation(tagetId: String) {
        scope.launch {
            val conversation = chatConversationRepository.getConversation(tagetId, Conversation.ConversationType.PRIVATE)
                ?: Conversation.obtain(Conversation.ConversationType.PRIVATE, tagetId, "")

            val otherUserinfo = ChatUserInfoCache.getOrFetchUserInfo(tagetId)
            _uiState.update {
                it.copy(
                    conversation = conversation,
                    otherUserinfo = otherUserinfo,
                    isCustomerServiceConversation = false,
                )
            }

            getMessages(conversation)
            markAllAsRead(conversation)
        }
    }

    private suspend fun getMessages(conversation: Conversation, direction: HistoryMessageDirection = HistoryMessageDirection.FRONT) {
        val datetime = when (direction) {
            HistoryMessageDirection.FRONT -> _uiState.value.messages.lastOrNull()?.sentTime ?: 0L
            HistoryMessageDirection.BACK -> _uiState.value.messages.firstOrNull()?.sentTime ?: 0L
        }

        var messages = chatMessageRepository.getMessages(
            conversation,
            datetime,
            order = when (direction) {
                HistoryMessageDirection.FRONT -> HistoryMessageOption.PullOrder.DESCEND
                HistoryMessageDirection.BACK -> HistoryMessageOption.PullOrder.ASCEND
            }
        )

        /**
         * 把他们的排序都修改为从旧到新
         */
        messages = when (direction) {
            HistoryMessageDirection.FRONT -> messages
            HistoryMessageDirection.BACK -> messages
        }

        _uiState.update {
            val newMessages = when (direction) {
                HistoryMessageDirection.FRONT -> it.messages + messages
                HistoryMessageDirection.BACK -> messages + it.messages
            }

            it.copy(messages = newMessages)
        }
    }

    private suspend fun markAllAsRead(conversation: Conversation) {
        chatMessageRepository.markAllMessagesAsRead(conversation)
    }

    fun openOrPreviewMediaMessage(message: Message) {
        fun navigateToMediaPreview() {
            val mediaList = message.getMediaList() ?: return
            val route = MediaPreviewRoute(mediaList)
            navCenter.navigateToMediaPreview(route)
        }

        if (message.messageDirection == Message.MessageDirection.SEND) { //发送方可以直接预览
            navigateToMediaPreview()
            return
        }

        val lockStatus = message.lockStatus()

        when(lockStatus) {
            PrivateMessageLockStatus.LOCKED -> {
                scope.launch { chatMessageRepository.openPrivateMessage(message) }
            }
            PrivateMessageLockStatus.OPENED -> navigateToMediaPreview()
            PrivateMessageLockStatus.EXPIRED -> {} //过期的可以不用管
        }
    }

    fun openOrPlayGiftMessage(message: Message) {
        val data = (message.content as? BaseCustomMessageContent)?.get(GiftResourceInfo::class.java) ?: return

        scope.launch {
            val gift = giftPreloadManager.getGiftEntity(giftId = data.giftId)

            if (message.messageDirection == Message.MessageDirection.SEND) { //发送方直接预览礼物

                _uiState.update { it.copy(currentPlayerGift = gift) }
            } else {
                val status = message.unwrapStatus()
                when(status) {
                    UnwrapStatus.UNWRAPPED -> { //礼物未拆封，先调用接口拆封，再展示
                        //TODO 先调用接口拆封礼物
                        delay(2000)
                        _uiState.update { it.copy(currentPlayerGift = gift) }
                    }
                    UnwrapStatus.WRAPPED -> { //礼物已经拆封
                        _uiState.update { it.copy(currentPlayerGift = gift) }
                    }
                }
            }
        }
    }

    fun stopPlayGiftMessage() {
        _uiState.update { it.copy(currentPlayerGift = null) }
    }

    fun sendTextMessage() {
        val text = uiState.value.inputValue
        if (text.isEmpty()) return

        sendTextMessage(text)
        _uiState.update { it.copy(inputValue = "") }
    }

    fun resendMessage(message: Message) {
        scope.launch {
            when (message.content) {
                is TextMessage,
                is GiftMessageContent,
                is PrivateMessageContent,
                is MultiPrivateMessageContent,
                is PublicMessageContent -> chatMessageRepository.sendMessage(message).collect { updatePendingMessage(it) }

                is MediaMessageContent -> chatMessageRepository.sendMediaMessage(message).collect { updatePendingMessage(it) }
            }
        }
    }

    fun sendTextMessage(text: String?) {
        if (text.isNullOrEmpty()) return

        scope.launch {
            val conversation = uiState.value.conversation ?: return@launch
            val pendingMessage = chatMessageRepository.obtainTextMessage(conversation, text)

            chatMessageRepository.sendMessage(pendingMessage).collect { updatePendingMessage(it) }
        }
    }

    fun sendGiftMessage(gift: GiftEntity) {
        scope.launch {
            val conversation = uiState.value.conversation ?: return@launch
            val pendingMessage = chatMessageRepository.obtainGiftMessage(conversation, gift)

            chatMessageRepository.sendMessage(pendingMessage).collect { updatePendingMessage(it) }
        }
    }

    fun sendNormalMomentsMessage(uri: Uri) {
        scope.launch {
            val conversation = uiState.value.conversation ?: return@launch
            val pendingMessage = chatMessageRepository.obtainMediaMessage(conversation, uri) ?: return@launch

            chatMessageRepository.sendMediaMessage(pendingMessage).collect { updatePendingMessage(it) }
        }
    }

    fun sendPrivateMoments(selectedMediaList: List<MediaItemEntity>) {
        scope.launch {
            val conversation = uiState.value.conversation ?: return@launch
            val pendingMessage = chatMessageRepository.obtainPrivateMessage(conversation, selectedMediaList) ?: return@launch

            chatMessageRepository.sendMediaMessage(pendingMessage).collect { updatePendingMessage(it) }
        }
    }

    fun updateInputValue(value: String) {
        _uiState.update { it.copy(inputValue = value) }
    }

    override fun onCleared() {
        messageMonitor.removeMessageListener(this)
        messageExpansionReceiverMonitor.removeListener(this)
        messageRecallMonitor.removeListener(this)
        super.onCleared()
    }

    private fun updatePendingMessage(pendingMessage: Message?) {
        if (pendingMessage == null) return

        Timber.d(TAG, "updatePendingMessage: ${pendingMessage.messageId}, ${pendingMessage.sentStatus}")

        _uiState.update {
            val messages = it.messages.toMutableList()
            val index = messages.indexOfFirst { message -> message.messageId == pendingMessage.messageId }

            if (index == -1) {
                messages.add(0, pendingMessage) //因为列表反向了
            } else {
                // 强制创建新的列表以确保状态变化被检测到
                messages[index] = pendingMessage
            }

            Timber.d(TAG, "dataInUpdate=${if (index == -1) messages[0] else messages[index]}")
            // 创建新的列表实例以确保Compose检测到变化
            it.copy(messages = messages.toList())
        }
    }

    private fun shouldUpdateStatus(current: Message.SentStatus, new: Message.SentStatus): Boolean {
        return when (current) {
            Message.SentStatus.SENT -> false // 已发送成功，不允许倒退
            Message.SentStatus.FAILED -> new != Message.SentStatus.SENDING // 失败状态不能回到发送中
            Message.SentStatus.SENDING -> true // 发送中可以更新到任何状态
            else -> true
        }
    }

    enum class HistoryMessageDirection {
        FRONT,

        BACK,
    }

    companion object {
        private const val TAG = "ChatMessageViewModel"
    }
}

sealed interface ConversationType {
    data class Private(val targetId: String) : ConversationType
    data object CustomerService : ConversationType
}

package com.flutterup.gifts.entity

import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "gift_cache")
data class GiftCacheEntity(
    @PrimaryKey val giftId: String,

    val name: String,     // 礼物名称

    val imagePath: String?, // 图片路径

    val gifPath: String?, // gif路径

    val videoPath: String?, // 视频路径

    val version: Long,     // 礼物版本

    val desc: String?, // 礼物描述

    val lastUpdated: Long = System.currentTimeMillis() // 最后一次更新时间
)
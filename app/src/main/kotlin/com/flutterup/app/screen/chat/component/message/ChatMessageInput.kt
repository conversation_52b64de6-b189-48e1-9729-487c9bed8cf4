@file:OptIn(ExperimentalLayoutApi::class)

package com.flutterup.app.screen.chat.component.message

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.flutterup.app.R
import com.flutterup.app.design.component.AppTextField
import com.flutterup.app.design.modifiers.noRippleClickable
import com.flutterup.app.design.theme.LabelBackgroundPrimary
import com.flutterup.app.design.theme.PurpleTextFieldColors
import com.flutterup.app.design.theme.TextBlack
import com.flutterup.app.design.theme.TextBlack666
import com.flutterup.app.model.UserInfo
import com.flutterup.app.screen.chat.ChatGiftDialogScreen
import com.flutterup.app.screen.chat.state.ChatMessageUiState
import com.flutterup.gifts.entity.GiftEntity


/**
 * @param backgroundCornerSize
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun ChatMessageInput(
    value: String,
    uiState: ChatMessageUiState,
    modifier: Modifier = Modifier,
    enable: Boolean = true,
    backgroundCornerSize: Dp = 24.dp,
    inputBackgroundCornerSize: Dp = 8.dp,
    onValueChange: (String) -> Unit = {},
    onSendClick: () -> Unit = {},
    onGiftSendClick: (GiftEntity) -> Unit = {},
    onNormalMomentsClick: () -> Unit = {},
    onPrivateMomentsClick: () -> Unit = {},
) {
    val focusManager = LocalFocusManager.current

    var isShowGift by remember { mutableStateOf(false) }
    var isShowMore by remember { mutableStateOf(false) }

    Box(
        modifier = modifier
            .background(
                color = Color.White,
                shape = RoundedCornerShape(
                    topStart = backgroundCornerSize,
                    topEnd = backgroundCornerSize,
                )
            )
            .fillMaxWidth()
            .wrapContentHeight()
            .padding(top = 15.dp, bottom = 10.dp)
            .navigationBarsPadding()
            .imePadding()
    ) {
        Column {
            InputContent(
                value = value,
                enable = enable,
                giftEnable = !uiState.isCustomerServiceConversation, //非客服
                onValueChange = onValueChange,
                onSendClick = onSendClick,
                inputBackgroundCornerSize = inputBackgroundCornerSize,
                onInputFocusChanged = { isFocused ->
                    if (isFocused) {
                        isShowGift = false
                        isShowMore = false
                    }
                },
                onGiftClick = {
                    focusManager.clearFocus()
                    isShowMore = false
                    isShowGift = true
                },
                onMoreClick = {
                    focusManager.clearFocus()
                    isShowGift = false
                    isShowMore = true
                }
            )

            if (isShowMore) {
                MoreContent(
                    uiState = uiState,
                    enable = enable,
                    onNormalMomentsClick = onNormalMomentsClick,
                    onPrivateMomentsClick = onPrivateMomentsClick,
                )
            }
        }
    }

    if (enable) {
        ChatGiftDialogScreen(
            isShown = isShowGift,
            userInfo = uiState.mineUserinfo,
            onDismissRequest = { isShowGift = false },
            onGiftSendClick = onGiftSendClick
        )
    }
}

@Composable
private fun InputContent(
    value: String,
    enable: Boolean,
    giftEnable: Boolean,
    onValueChange: (String) -> Unit,
    onInputFocusChanged: (Boolean) -> Unit,
    onSendClick: () -> Unit,
    onGiftClick: () -> Unit,
    onMoreClick: () -> Unit,
    inputBackgroundCornerSize: Dp,
) {
    val trailingIcon = if (value.isNotEmpty()) {
        @Composable {
            Icon(
                painter = painterResource(R.drawable.ic_chat_white_send),
                contentDescription = null,
                tint = LabelBackgroundPrimary,
                modifier = Modifier.noRippleClickable(onClick = onSendClick)
            )
        }
    } else null

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .padding(horizontal = 15.dp)
    ) {
        AppTextField(
            value = value,
            onValueChange = onValueChange,
            modifier = Modifier
                .weight(1f)
                .heightIn(max = 200.dp)
                .clip(RoundedCornerShape(inputBackgroundCornerSize))
                .onFocusChanged {
                    onInputFocusChanged(it.isFocused)
                },
            colors = PurpleTextFieldColors,
            contentPadding = PaddingValues(horizontal = 11.dp, vertical = 13.dp),
            enabled = enable,
            placeholder = {
                Text(
                    text = stringResource(R.string.your_message),
                    style = TextStyle(
                        fontSize = 14.sp,
                        lineHeight = 16.sp,
                        fontWeight = FontWeight.W300,
                        color = TextBlack666,
                    )
                )
            },
            textStyle = TextStyle(
                fontSize = 14.sp,
                lineHeight = 16.sp,
                fontWeight = FontWeight.W400,
                color = TextBlack,
            ),
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Send
            ),
            keyboardActions = KeyboardActions(onSend = { onSendClick() }),
            trailingIcon = trailingIcon,
            singleLine = false,
        )

        Spacer(Modifier.width(21.dp))

        //Gift
        if (giftEnable) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .size(30.dp)
                    .background(ICON_BACKGROUND_COLOR, CircleShape)
                    .align(Alignment.CenterVertically)
                    .noRippleClickable(onClick = onGiftClick)
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_chat_gift),
                    contentDescription = null,
                    tint = Color.Unspecified,
                    modifier = Modifier.size(24.dp)
                )
            }

            Spacer(
                modifier = Modifier.width(16.dp)
            )
        }

        //album
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .size(30.dp)
                .background(ICON_BACKGROUND_COLOR, CircleShape)
                .align(Alignment.CenterVertically)
                .noRippleClickable(onClick = onMoreClick)
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_chat_input_more),
                contentDescription = null,
                tint = Color.Unspecified,
            )
        }
    }
}


@Composable
private fun MoreContent(
    uiState: ChatMessageUiState,
    enable: Boolean,
    onNormalMomentsClick: () -> Unit = {},
    onPrivateMomentsClick: () -> Unit = {},
) {
    val normalMomentsText = if (uiState.isCustomerServiceConversation) {
        stringResource(R.string.chat_customer_service_album)
    } else {
        stringResource(R.string.chat_normal_moments)
    }

    Row(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .padding(top = 26.dp, bottom = 33.dp, start = 14.dp, end = 20.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        val sizeModifier = if (uiState.isOnlyNormalMoments)
            Modifier.size(180.dp, 47.dp)
        else
            Modifier.weight(1f).height(47.dp)

        Box(
            modifier = Modifier
                .then(sizeModifier)
                .noRippleClickable(
                    enabled = enable,
                    onClick = onNormalMomentsClick
                ),
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(R.mipmap.ic_chat_normal_moments_bg),
                contentDescription = null,
                contentScale = ContentScale.FillBounds,
                modifier = Modifier.matchParentSize()
            )

            Row(
                modifier = Modifier
                    .padding(start = 18.dp, end = 16.dp)
                    .matchParentSize(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = normalMomentsText,
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.W900,
                        color = Color(0xFF796ADE),
                    )
                )

                Image(
                    painter = painterResource(R.mipmap.ic_chat_normal_monents),
                    contentDescription = null,
                    modifier = Modifier.size(32.dp)
                )
            }
        }

        if (!uiState.isOnlyNormalMoments) {
            Spacer(Modifier.width(21.dp))

            Box(
                modifier = Modifier
                    .then(sizeModifier)
                    .noRippleClickable(
                        enabled = enable,
                        onClick = onPrivateMomentsClick
                    ),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(R.mipmap.ic_chat_private_moments_bg),
                    contentDescription = null,
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier.matchParentSize()
                )

                Row(
                    modifier = Modifier
                        .padding(start = 14.dp, end = 12.dp)
                        .matchParentSize(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.chat_private_moments),
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight.W900,
                            color = Color(0xFFBA31B1),
                        )
                    )

                    Image(
                        painter = painterResource(R.mipmap.ic_chat_private_monents),
                        contentDescription = null,
                        modifier = Modifier.size(32.dp)
                    )
                }
            }
        }
    }
}

private val ICON_BACKGROUND_COLOR = Color(0xFFE8E4FE)

@Preview
@Composable
private fun ChatMessageInputPreview() {
    Surface {
        Box(
            modifier = Modifier.fillMaxWidth().background(Color.Black),
            contentAlignment = Alignment.BottomCenter
        ) {
            ChatMessageInput(
                value = "knakjndkjodsamksnjkasdnjknajkdsnkndkjasnkjsnasdnsakjsdansdnjksdnjkdanjksdjnasansjdkknakjndkjodsamksnjkasdnjknajkdsnkndkjasnkjsnasdnsakjsdansdnjksdnjkdanjksdjnasansjdk",
                uiState = ChatMessageUiState(
                    mineUserinfo = UserInfo(scene = 1)
                )
            )
        }
    }
}
